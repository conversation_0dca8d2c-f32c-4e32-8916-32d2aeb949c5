# BTC Trading Bot PROFESIONAL v4.2 - VERSIÓN MEJORADA ANTI-OVERFITTING
# Sistema completo con ML balanceado, modelos optimizados y SIN trailing stop/profit
# Diseñado para máxima robustez con niveles fijos de TP/SL
#
# MEJORAS v4.2:
# - Umbrales de confianza y consenso más permisivos (15% y 35%)
# - Modelos ML con configuración balanceada (menos restrictiva)
# - Criterios de validación más flexibles para aceptar más modelos
# - Filtros de señales ultra permisivos con múltiples niveles
# - Más features seleccionados (15 en lugar de 10)
# - Validación cruzada menos estricta (5 folds en lugar de 7)
# - Corrección de warnings de diversidad de señales
# - Mejora en la lógica de filtrado de predicciones
# - Filtro anti-sesgo para rechazar predicciones extremadamente concentradas
# - Estadísticas de filtrado corregidas y más claras

import ccxt
import pandas as pd
import numpy as np
import talib
import time
import datetime
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.ensemble import (RandomForestClassifier, VotingClassifier, GradientBoostingClassifier,
                             ExtraTreesClassifier, StackingClassifier)
from sklearn.linear_model import LogisticRegression, RidgeClassifier, Perceptron
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.utils.class_weight import compute_class_weight
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, f1_score
from sklearn.feature_selection import SelectKBest, f_classif, RFE, SelectFromModel
from sklearn.decomposition import PCA
from xgboost import XGBClassifier
import lightgbm as lgb
try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False
    print("⚠️ CatBoost no disponible - instalando modelos alternativos")

import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
import warnings
from scipy import stats
from scipy.optimize import minimize
import joblib
from collections import defaultdict
warnings.filterwarnings('ignore')

# Configuración DEMO ACTIVA - VERSIÓN ULTRA ROBUSTA v4.5 DATOS HISTÓRICOS CORREGIDOS
# 🚀 MEJORAS v4.5 IMPLEMENTADAS:
# ✅ 1. BUG CRÍTICO CORREGIDO: Descarga real de datos históricos (antes solo 42 días)
# ✅ 2. Descarga múltiple para períodos largos (365+ días reales)
# ✅ 3. Datos históricos aumentados: 1000 → 2500+ velas para mejor entrenamiento
# ✅ 4. Parámetros de regularización ajustados para reducir sesgos en modelos
# ✅ 5. Umbrales de sesgo más permisivos: 70% → 55% para mayor diversidad
# ✅ 6. Confianza mínima reducida: 45% → 40% para más señales
# ✅ 7. Consenso reducido: 70% → 55% para mayor flexibilidad
# ✅ 8. Modelos menos restrictivos: más estimadores, menos regularización
LOOKBACK = 50  # Incrementado para mejor contexto histórico
SYMBOL = 'BTC/USDT'  # Cambiar aquí para otro activo: 'ETH/USDT', 'SOL/USDT', etc.
INITIAL_CAPITAL = 10000
MAX_RISK_PER_TRADE = 0.015  # 1.5% por trade (más conservador)
MAX_DAILY_LOSS = 0.04  # 4% pérdida máxima diaria (más conservador)
MIN_WIN_RATE = 0.45  # 45% mínimo (más exigente)
MIN_PROFIT_FACTOR = 1.2  # Más exigente
MIN_CONFIDENCE = 0.40  # REDUCIDO de 0.45 a 0.40 (40%) para mayor diversidad de señales
MAX_POSITIONS = 1  # Solo una posición por vez

# PARÁMETROS AVANZADOS DE ROBUSTEZ
TP_SL_TOLERANCE = 0.0005  # 0.05% tolerancia para TP/SL
TRAILING_STOP_ENABLED = False  # SIN trailing stop - mantener niveles originales
TRAILING_PROFIT_ENABLED = False  # SIN trailing profit - mantener niveles originales
ATR_MULTIPLIER_SL = 2.0  # Multiplicador ATR para stop loss (más ajustado)
ATR_MULTIPLIER_TP = 5.0  # Multiplicador ATR para take profit (mejor R:R 1:2.5)
ENSEMBLE_MIN_AGREEMENT = 0.55  # REDUCIDO de 0.70 a 0.55 (55%) para mayor diversidad
VOLATILITY_FILTER = True  # Filtrar mercados de alta volatilidad
MAX_VOLATILITY_THRESHOLD = 0.035  # 3.5% volatilidad máxima (más conservador)
FEATURE_SELECTION_ENABLED = True  # Selección automática de features

# NUEVOS PARÁMETROS v4.2 - ROBUSTEZ AVANZADA MEJORADA
WALK_FORWARD_ENABLED = True  # Validación Walk-Forward
WALK_FORWARD_WINDOW = 120  # REDUCIDO de 150 a 120 para validación menos estricta
STACKING_ENABLED = True  # Ensemble con Stacking
EARLY_STOPPING_ENABLED = True  # Early stopping para prevenir overfitting
CROSS_VALIDATION_FOLDS = 5  # REDUCIDO de 7 a 5 para validación menos estricta
MODEL_PERSISTENCE_ENABLED = True  # Guardar/cargar modelos entrenados
FEATURE_IMPORTANCE_THRESHOLD = 0.005  # Umbral más bajo para capturar más features importantes
OUTLIER_DETECTION_ENABLED = True  # Detección y filtrado de outliers
ADAPTIVE_THRESHOLDS = True  # Umbrales adaptativos basados en volatilidad del mercado
MODEL_MONITORING_ENABLED = True  # Monitoreo de degradación del modelo
RETRAINING_THRESHOLD = 0.08  # Umbral más estricto para reentrenamiento (8%)
MAX_FEATURES_SELECTED = 15  # AUMENTADO de 10 a 15 para mejor performance
REGULARIZATION_STRENGTH = 0.15  # REDUCIDO de 0.2 a 0.15 para menos restricción
ENSEMBLE_DIVERSITY_THRESHOLD = 0.20  # REDUCIDO de 0.25 a 0.20 para mayor diversidad
HYPERPARAMETER_OPTIMIZATION = True  # Optimización de hiperparámetros
ADVANCED_FEATURE_ENGINEERING = True  # Feature engineering avanzado
ENSEMBLE_WEIGHTING_METHOD = 'performance_based'  # Método de pesos basado en performance

# Crear directorios
for dir_name in ['modelos', 'logs', 'trades', 'reports']:
    os.makedirs(dir_name, exist_ok=True)

class TradingLogger:
    """Sistema de logging mejorado"""
    def __init__(self):
        self.log_file = f"logs/trading_{datetime.datetime.now().strftime('%Y%m%d')}.log"

    def log(self, message, level="INFO"):
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"{timestamp} [{level}] {message}"
        print(formatted_message)

        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(formatted_message + "\n")
        except:
            pass

logger = TradingLogger()

class ModelValidator:
    """Validación temporal robusta y prevención de overfitting"""

    def __init__(self):
        self.validation_scores = {}
        self.feature_importance_history = {}
        self.model_performance_history = defaultdict(list)

    def walk_forward_validation(self, X, y, model, window_size=WALK_FORWARD_WINDOW):
        """Validación Walk-Forward para series temporales"""
        if not WALK_FORWARD_ENABLED or len(X) < window_size * 2:
            return None

        logger.log("🔄 Ejecutando validación Walk-Forward...")

        scores = []
        n_splits = (len(X) - window_size) // (window_size // 4)  # Overlap del 75%

        for i in range(n_splits):
            start_train = i * (window_size // 4)
            end_train = start_train + window_size
            start_test = end_train
            end_test = min(start_test + (window_size // 4), len(X))

            if end_test <= start_test:
                break

            X_train_fold = X[start_train:end_train]
            y_train_fold = y[start_train:end_train]
            X_test_fold = X[start_test:end_test]
            y_test_fold = y[start_test:end_test]

            try:
                # Clonar modelo para evitar contaminación
                model_clone = model.__class__(**model.get_params())
                model_clone.fit(X_train_fold, y_train_fold)
                score = model_clone.score(X_test_fold, y_test_fold)
                scores.append(score)
            except Exception as e:
                logger.log(f"⚠️ Error en fold Walk-Forward: {str(e)}", "WARNING")
                continue

        if scores:
            mean_score = np.mean(scores)
            std_score = np.std(scores)
            logger.log(f"📊 Walk-Forward Score: {mean_score:.3f} ± {std_score:.3f}")
            return {'mean': mean_score, 'std': std_score, 'scores': scores}

        return None

    def temporal_cross_validation(self, X, y, model, n_splits=CROSS_VALIDATION_FOLDS):
        """Validación cruzada temporal específica para series de tiempo"""
        logger.log("⏰ Ejecutando validación cruzada temporal...")

        tscv = TimeSeriesSplit(n_splits=n_splits)
        scores = []

        for train_idx, test_idx in tscv.split(X):
            X_train_fold = X[train_idx]
            y_train_fold = y[train_idx]
            X_test_fold = X[test_idx]
            y_test_fold = y[test_idx]

            try:
                model_clone = model.__class__(**model.get_params())
                model_clone.fit(X_train_fold, y_train_fold)
                score = model_clone.score(X_test_fold, y_test_fold)
                scores.append(score)
            except Exception as e:
                logger.log(f"⚠️ Error en fold temporal: {str(e)}", "WARNING")
                continue

        if scores:
            mean_score = np.mean(scores)
            std_score = np.std(scores)
            logger.log(f"📊 Validación Temporal Score: {mean_score:.3f} ± {std_score:.3f}")
            return {'mean': mean_score, 'std': std_score, 'scores': scores}

        return None

    def detect_overfitting(self, train_score, val_score, threshold=0.25):
        """Detecta overfitting comparando scores de entrenamiento y validación - MEJORADO v4.2"""
        if train_score is None or val_score is None:
            return False

        overfitting_gap = train_score - val_score
        is_overfitting = overfitting_gap > threshold

        if is_overfitting:
            logger.log(f"🚨 OVERFITTING DETECTADO: Gap = {overfitting_gap:.3f}", "WARNING")

        return is_overfitting

    def calculate_model_stability(self, model_name, current_score):
        """Calcula estabilidad del modelo a lo largo del tiempo"""
        self.model_performance_history[model_name].append(current_score)

        if len(self.model_performance_history[model_name]) < 5:
            return 1.0  # Estabilidad perfecta si no hay suficiente historia

        recent_scores = self.model_performance_history[model_name][-5:]
        stability = 1.0 - np.std(recent_scores)  # Menor variabilidad = mayor estabilidad

        return max(0.0, stability)

class AdvancedFeatureSelector:
    """Selección avanzada de features con múltiples métodos"""

    def __init__(self):
        self.selected_features = None
        self.feature_scores = {}
        self.feature_importance = {}

    def remove_outliers(self, X, y, contamination=0.1):
        """Detecta y remueve outliers usando Isolation Forest"""
        if not OUTLIER_DETECTION_ENABLED:
            return X, y

        logger.log("🔍 Detectando outliers...")

        try:
            from sklearn.ensemble import IsolationForest

            iso_forest = IsolationForest(contamination=contamination, random_state=42)
            outlier_labels = iso_forest.fit_predict(X)

            # Mantener solo inliers (etiqueta 1)
            inlier_mask = outlier_labels == 1
            X_clean = X[inlier_mask]
            y_clean = y[inlier_mask]

            outliers_removed = len(X) - len(X_clean)
            logger.log(f"🧹 Outliers removidos: {outliers_removed} ({outliers_removed/len(X)*100:.1f}%)")

            return X_clean, y_clean

        except Exception as e:
            logger.log(f"⚠️ Error removiendo outliers: {str(e)}", "WARNING")
            return X, y

    def correlation_filter(self, df, threshold=0.95):
        """Remueve features altamente correlacionados"""
        logger.log("🔗 Filtrando features correlacionados...")

        # Calcular matriz de correlación
        corr_matrix = df.corr().abs()

        # Encontrar pares altamente correlacionados
        upper_triangle = corr_matrix.where(
            np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
        )

        # Encontrar features a remover
        to_drop = [column for column in upper_triangle.columns
                  if any(upper_triangle[column] > threshold)]

        logger.log(f"📉 Features correlacionados removidos: {len(to_drop)}")

        return df.drop(columns=to_drop)

    def select_best_features(self, X, y, feature_names, max_features=MAX_FEATURES_SELECTED):
        """Selección inteligente de features usando múltiples métodos"""
        logger.log("🎯 Seleccionando mejores features...")

        feature_scores = {}

        # Método 1: Univariate Feature Selection
        try:
            selector_univariate = SelectKBest(score_func=f_classif, k=min(max_features*2, X.shape[1]))
            selector_univariate.fit(X, y)

            univariate_scores = selector_univariate.scores_
            for i, score in enumerate(univariate_scores):
                if i < len(feature_names):
                    feature_scores[feature_names[i]] = feature_scores.get(feature_names[i], 0) + score

        except Exception as e:
            logger.log(f"⚠️ Error en selección univariada: {str(e)}", "WARNING")

        # Método 2: Random Forest Feature Importance
        try:
            rf_selector = RandomForestClassifier(n_estimators=50, random_state=42, n_jobs=-1)
            rf_selector.fit(X, y)

            rf_importance = rf_selector.feature_importances_
            for i, importance in enumerate(rf_importance):
                if i < len(feature_names):
                    feature_scores[feature_names[i]] = feature_scores.get(feature_names[i], 0) + importance * 1000

        except Exception as e:
            logger.log(f"⚠️ Error en importancia RF: {str(e)}", "WARNING")

        # Método 3: L1 Regularization (Lasso)
        try:
            from sklearn.feature_selection import SelectFromModel
            from sklearn.linear_model import LassoCV

            lasso = LassoCV(cv=3, random_state=42, max_iter=1000)
            lasso_selector = SelectFromModel(lasso)
            lasso_selector.fit(X, y)

            selected_mask = lasso_selector.get_support()
            for i, selected in enumerate(selected_mask):
                if i < len(feature_names) and selected:
                    feature_scores[feature_names[i]] = feature_scores.get(feature_names[i], 0) + 100

        except Exception as e:
            logger.log(f"⚠️ Error en selección Lasso: {str(e)}", "WARNING")

        # Seleccionar top features
        if feature_scores:
            sorted_features = sorted(feature_scores.items(), key=lambda x: x[1], reverse=True)
            selected_features = [feat[0] for feat in sorted_features[:max_features]]

            self.selected_features = selected_features
            self.feature_scores = feature_scores

            logger.log(f"✅ Seleccionados {len(selected_features)} mejores features")
            logger.log(f"🏆 Top 5 features: {selected_features[:5]}")

            return selected_features

        logger.log("⚠️ No se pudieron seleccionar features, usando todos", "WARNING")
        return feature_names[:max_features]

class DataManager:
    """Gestión profesional de datos"""
    def __init__(self):
        self.exchange = None

    def get_live_data(self, symbol=SYMBOL, timeframe='15m', limit=2500):
        """Obtiene datos en vivo de Binance - MEJORADO con descarga múltiple para más datos históricos"""
        try:
            if not self.exchange:
                self.exchange = ccxt.binance({
                    'enableRateLimit': True,
                    'options': {'defaultType': 'spot'}
                })

            # Si necesitamos más de 1000 velas, hacer múltiples llamadas hacia atrás
            if limit > 1000:
                logger.log(f"📚 Descargando {limit} velas en múltiples llamadas...")
                all_data = []
                calls_needed = (limit + 999) // 1000  # Redondear hacia arriba

                # Empezar desde el presente y ir hacia atrás
                for call_num in range(calls_needed):
                    current_limit = min(1000, limit - len(all_data))
                    if current_limit <= 0:
                        break

                    try:
                        # Calcular timestamp de inicio para esta llamada
                        # Cada llamada va más atrás en el tiempo
                        hours_back = len(all_data)
                        since_timestamp = None

                        if hours_back > 0:
                            # Calcular timestamp para ir hacia atrás
                            now = int(time.time() * 1000)
                            since_timestamp = now - (hours_back * 60 * 60 * 1000)  # 1 hora = 3600000 ms

                        if since_timestamp:
                            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe=timeframe,
                                                            limit=current_limit, since=since_timestamp)
                        else:
                            # Primera llamada - obtener datos más recientes
                            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe=timeframe,
                                                            limit=current_limit)

                        if not ohlcv:
                            logger.log(f"⚠️ No hay más datos disponibles en llamada {call_num + 1}")
                            break

                        # Insertar al principio para mantener orden cronológico
                        all_data = ohlcv + all_data

                        # Rate limiting
                        time.sleep(0.2)

                        logger.log(f"📊 Descargadas {len(all_data)} velas de {limit} solicitadas... (llamada {call_num + 1}/{calls_needed})")

                        if len(ohlcv) < current_limit:
                            logger.log(f"⚠️ Datos limitados disponibles: {len(ohlcv)} < {current_limit}")
                            break  # No hay más datos disponibles

                    except Exception as e:
                        logger.log(f"⚠️ Error en descarga múltiple (llamada {call_num + 1}): {str(e)}", "WARNING")
                        break

                if all_data:
                    # Remover duplicados y ordenar
                    df = pd.DataFrame(all_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df = df.drop_duplicates(subset=['timestamp']).sort_values('timestamp')
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)

                    logger.log(f"✅ Datos obtenidos: {len(df)} velas de {timeframe}")
                    return df
                else:
                    logger.log("⚠️ No se pudieron obtener datos, usando simulados", "WARNING")
                    return self.get_mock_data(timeframe, limit)
            else:
                # Llamada simple para límites <= 1000
                ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)

                logger.log(f"✅ Datos obtenidos: {len(df)} velas de {timeframe}")
                return df

        except Exception as e:
            logger.log(f"❌ Error obteniendo datos: {str(e)}", "ERROR")
            return self.get_mock_data(timeframe, limit)

    def get_mock_data(self, timeframe='15m', limit=2500):
        """Datos simulados realistas para testing"""
        np.random.seed(42)
        freq_map = {'5m': '5T', '15m': '15T', '1h': '1H', '2h': '2H', '4h': '4H'}

        timestamps = pd.date_range(end=pd.Timestamp.now(), periods=limit, freq=freq_map[timeframe])

        # Simulación más realista con tendencias y volatilidad variable
        # Ajustar base_price según el activo
        base_prices = {
            'BTC/USDT': 98000,
            'ETH/USDT': 3500,
            'SOL/USDT': 140,
            'BNB/USDT': 600
        }
        base_price = base_prices.get(SYMBOL, 98000)

        drift = 0.0001
        volatility = 0.001

        prices = [base_price]
        for i in range(1, limit):
            change = drift + np.random.normal(0, volatility)
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)

        close = np.array(prices)
        open_prices = np.roll(close, 1)
        open_prices[0] = base_price

        high = np.maximum(close, open_prices) * (1 + np.random.uniform(0, 0.002, limit))
        low = np.minimum(close, open_prices) * (1 - np.random.uniform(0, 0.002, limit))
        volume = np.random.lognormal(10, 0.5, limit) * 1000

        df = pd.DataFrame({
            'open': open_prices,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        }, index=timestamps)

        logger.log(f"✅ Datos simulados generados: {len(df)} velas")
        return df

class FeatureEngineer:
    """Feature Engineering avanzado y optimizado"""

    @staticmethod
    def add_price_features(df):
        """Features básicos de precio mejorados"""
        # Returns múltiples períodos
        df['returns'] = df['close'].pct_change()
        df['returns_2'] = df['close'].pct_change(2)
        df['returns_5'] = df['close'].pct_change(5)
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))

        # Volatility expandida
        df['volatility'] = df['log_returns'].rolling(20).std()
        df['volatility_5'] = df['log_returns'].rolling(5).std()
        df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(50).mean()
        df['volatility_rank'] = df['volatility'].rolling(100).rank(pct=True)

        # Price ratios mejorados
        df['hl_ratio'] = (df['high'] - df['low']) / df['close']
        df['co_ratio'] = (df['close'] - df['open']) / df['open']
        df['oc_ratio'] = (df['open'] - df['close'].shift(1)) / df['close'].shift(1)

        # Momentum features
        df['price_momentum_5'] = df['close'] / df['close'].shift(5) - 1
        df['price_momentum_10'] = df['close'] / df['close'].shift(10) - 1
        df['price_acceleration'] = df['returns'] - df['returns'].shift(1)

        return df

    @staticmethod
    def add_technical_indicators(df):
        """Indicadores técnicos avanzados y optimizados"""
        # RSI múltiples períodos
        df['rsi_7'] = talib.RSI(df['close'], timeperiod=7)
        df['rsi_14'] = talib.RSI(df['close'], timeperiod=14)
        df['rsi_21'] = talib.RSI(df['close'], timeperiod=21)
        df['rsi_overbought'] = (df['rsi_14'] > 70).astype(int)
        df['rsi_oversold'] = (df['rsi_14'] < 30).astype(int)
        df['rsi_divergence'] = df['rsi_14'] - df['rsi_14'].shift(5)

        # Moving Averages expandidas
        df['sma_10'] = talib.SMA(df['close'], timeperiod=10)
        df['sma_20'] = talib.SMA(df['close'], timeperiod=20)
        df['sma_50'] = talib.SMA(df['close'], timeperiod=50)
        df['ema_8'] = talib.EMA(df['close'], timeperiod=8)
        df['ema_12'] = talib.EMA(df['close'], timeperiod=12)
        df['ema_26'] = talib.EMA(df['close'], timeperiod=26)

        # Cruces de medias móviles
        df['sma_cross_20_50'] = np.where(df['sma_20'] > df['sma_50'], 1, -1)
        df['ema_cross_12_26'] = np.where(df['ema_12'] > df['ema_26'], 1, -1)
        df['price_above_sma20'] = np.where(df['close'] > df['sma_20'], 1, 0)

        # MACD mejorado
        df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(df['close'])
        df['macd_cross'] = np.where(df['macd'] > df['macd_signal'], 1, -1)
        df['macd_momentum'] = df['macd_hist'] - df['macd_hist'].shift(1)
        df['macd_strength'] = abs(df['macd'] - df['macd_signal'])

        # Bollinger Bands avanzadas
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(df['close'])
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        df['bb_squeeze'] = (df['bb_width'] < df['bb_width'].rolling(20).quantile(0.2)).astype(int)

        # ATR - CRÍTICO PARA STOP LOSS Y TAKE PROFIT
        df['atr_14'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=14)
        df['atr_7'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=7)
        df['atr'] = df['atr_14']  # Mantener compatibilidad
        df['atr_ratio'] = df['atr'] / df['close']
        df['atr_trend'] = df['atr'] / df['atr'].rolling(20).mean()

        # Volume indicators avanzados
        df['volume_sma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        df['volume_trend'] = df['volume'].rolling(5).mean() / df['volume'].rolling(20).mean()
        df['obv'] = talib.OBV(df['close'], df['volume'])
        df['obv_trend'] = df['obv'] / df['obv'].rolling(20).mean()

        # Indicadores de momentum avanzados
        df['adx'] = talib.ADX(df['high'], df['low'], df['close'], timeperiod=14)
        df['cci'] = talib.CCI(df['high'], df['low'], df['close'], timeperiod=14)
        df['williams_r'] = talib.WILLR(df['high'], df['low'], df['close'], timeperiod=14)
        df['roc'] = talib.ROC(df['close'], timeperiod=10)
        df['mom'] = talib.MOM(df['close'], timeperiod=10)

        # Stochastic mejorado
        df['stoch_k'], df['stoch_d'] = talib.STOCH(df['high'], df['low'], df['close'])
        df['stoch_rsi_k'], df['stoch_rsi_d'] = talib.STOCHRSI(df['close'])
        df['stoch_cross'] = np.where(df['stoch_k'] > df['stoch_d'], 1, -1)

        # MFI y análisis de flujo de dinero
        df['mfi'] = talib.MFI(df['high'], df['low'], df['close'], df['volume'], timeperiod=14)
        df['mfi_overbought'] = (df['mfi'] > 80).astype(int)
        df['mfi_oversold'] = (df['mfi'] < 20).astype(int)

        return df

    @staticmethod
    def add_pattern_features(df):
        """Patrones de velas japonesas y estructura de mercado avanzados"""
        # Patrones básicos mejorados
        df['is_green'] = (df['close'] > df['open']).astype(int)
        df['body_size'] = abs(df['close'] - df['open']) / df['close']
        df['upper_shadow'] = (df['high'] - np.maximum(df['open'], df['close'])) / df['close']
        df['lower_shadow'] = (np.minimum(df['open'], df['close']) - df['low']) / df['close']
        df['total_range'] = (df['high'] - df['low']) / df['close']

        # Patrones consecutivos expandidos
        df['green_streak'] = df['is_green'].rolling(3).sum()
        df['red_streak'] = (1 - df['is_green']).rolling(3).sum()
        df['consecutive_direction'] = df['is_green'].rolling(5).sum()

        # Patrones de velas japonesas clásicos
        df['doji'] = (df['body_size'] < 0.002).astype(int)
        df['hammer'] = ((df['lower_shadow'] > 2 * df['body_size']) &
                       (df['upper_shadow'] < df['body_size'])).astype(int)
        df['shooting_star'] = ((df['upper_shadow'] > 2 * df['body_size']) &
                              (df['lower_shadow'] < df['body_size'])).astype(int)
        df['spinning_top'] = ((df['upper_shadow'] > df['body_size']) &
                             (df['lower_shadow'] > df['body_size']) &
                             (df['body_size'] < 0.005)).astype(int)

        # Análisis de estructura de mercado
        df['higher_high'] = (df['high'] > df['high'].shift(1)).astype(int)
        df['lower_low'] = (df['low'] < df['low'].shift(1)).astype(int)
        df['inside_bar'] = ((df['high'] < df['high'].shift(1)) &
                           (df['low'] > df['low'].shift(1))).astype(int)
        df['outside_bar'] = ((df['high'] > df['high'].shift(1)) &
                            (df['low'] < df['low'].shift(1))).astype(int)

        return df

    @staticmethod
    def add_market_structure_features(df):
        """Features de estructura de mercado y contexto avanzados"""
        # Niveles de soporte y resistencia mejorados
        df['resistance_level'] = df['high'].rolling(20).max()
        df['support_level'] = df['low'].rolling(20).min()
        df['resistance_level_50'] = df['high'].rolling(50).max()
        df['support_level_50'] = df['low'].rolling(50).min()
        df['distance_to_resistance'] = (df['resistance_level'] - df['close']) / df['close']
        df['distance_to_support'] = (df['close'] - df['support_level']) / df['close']
        df['distance_to_resistance_50'] = (df['resistance_level_50'] - df['close']) / df['close']
        df['distance_to_support_50'] = (df['close'] - df['support_level_50']) / df['close']

        # Análisis de tendencia mejorado
        df['trend_strength'] = (df['close'] - df['close'].shift(20)) / df['close'].shift(20)
        df['trend_strength_50'] = (df['close'] - df['close'].shift(50)) / df['close'].shift(50)
        df['trend_consistency'] = df['returns'].rolling(10).apply(lambda x: (x > 0).sum() / len(x))
        df['trend_acceleration'] = df['trend_strength'] - df['trend_strength'].shift(5)

        # Análisis de tiempo
        df['hour'] = df.index.hour if hasattr(df.index, 'hour') else 0
        df['day_of_week'] = df.index.dayofweek if hasattr(df.index, 'dayofweek') else 0
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
        df['is_market_open'] = ((df['hour'] >= 9) & (df['hour'] <= 16)).astype(int)

        # Features de contexto de mercado avanzados
        df['volume_price_trend'] = df['volume'] * df['returns']
        df['price_volume_correlation'] = df['returns'].rolling(20).corr(df['volume_ratio'])
        df['volume_momentum'] = df['volume'].rolling(5).mean() / df['volume'].rolling(20).mean()

        # Fractales y patrones de precio
        df['fractal_high'] = ((df['high'] > df['high'].shift(1)) &
                             (df['high'] > df['high'].shift(-1)) &
                             (df['high'] > df['high'].shift(2)) &
                             (df['high'] > df['high'].shift(-2))).astype(int)
        df['fractal_low'] = ((df['low'] < df['low'].shift(1)) &
                            (df['low'] < df['low'].shift(-1)) &
                            (df['low'] < df['low'].shift(2)) &
                            (df['low'] < df['low'].shift(-2))).astype(int)

        return df

    @staticmethod
    def add_advanced_features(df):
        """Features avanzados adicionales para mejorar performance"""
        if not ADVANCED_FEATURE_ENGINEERING:
            return df

        logger.log("🚀 Agregando features avanzados adicionales...")

        # Features de momentum avanzado
        df['momentum_divergence'] = df['roc'] - df['roc'].rolling(10).mean()
        df['price_momentum_ratio'] = df['price_momentum_5'] / (df['price_momentum_10'] + 1e-8)

        # Features de volatilidad avanzada
        df['volatility_breakout'] = (df['volatility'] > df['volatility'].rolling(50).quantile(0.8)).astype(int)
        df['volatility_regime'] = pd.qcut(df['volatility'].fillna(0), q=3, labels=[0, 1, 2], duplicates='drop')

        # Features de volumen avanzado
        df['volume_spike'] = (df['volume_ratio'] > 2.0).astype(int)
        df['volume_dryup'] = (df['volume_ratio'] < 0.5).astype(int)
        df['volume_price_divergence'] = np.where(
            (df['returns'] > 0) & (df['volume_ratio'] < 1), -1,
            np.where((df['returns'] < 0) & (df['volume_ratio'] > 1), 1, 0)
        )

        # Features de correlación cruzada
        df['rsi_macd_correlation'] = df['rsi_14'].rolling(20).corr(df['macd'])
        df['price_volume_sync'] = np.where(
            ((df['returns'] > 0) & (df['volume_ratio'] > 1)) |
            ((df['returns'] < 0) & (df['volume_ratio'] < 1)), 1, 0
        )

        # Features de estructura temporal
        df['intraday_range'] = (df['high'] - df['low']) / df['open']
        df['gap_up'] = np.where(df['open'] > df['close'].shift(1) * 1.002, 1, 0)
        df['gap_down'] = np.where(df['open'] < df['close'].shift(1) * 0.998, 1, 0)

        # Features de regímenes de mercado
        df['bull_market'] = (df['sma_20'] > df['sma_50']).astype(int)
        df['bear_market'] = (df['sma_20'] < df['sma_50']).astype(int)
        df['sideways_market'] = ((abs(df['trend_strength']) < 0.02) &
                                (df['adx'] < 25)).astype(int)

        return df

    @staticmethod
    def add_predictive_features(df):
        """Features específicamente diseñados para predicción de siguiente vela"""
        logger.log("🎯 Creando features predictivos optimizados...")

        # 1. PATRONES DE PRECIO DE CORTO PLAZO
        # Momentum inmediato
        df['momentum_1'] = df['close'].pct_change(1)
        df['momentum_2'] = df['close'].pct_change(2)
        df['momentum_3'] = df['close'].pct_change(3)
        df['momentum_5'] = df['close'].pct_change(5)

        # Aceleración del momentum
        df['momentum_acceleration'] = df['momentum_1'] - df['momentum_1'].shift(1)

        # Mean reversion signals
        df['distance_from_mean_5'] = (df['close'] - df['close'].rolling(5).mean()) / df['close']
        df['distance_from_mean_10'] = (df['close'] - df['close'].rolling(10).mean()) / df['close']
        df['distance_from_mean_20'] = (df['close'] - df['close'].rolling(20).mean()) / df['close']

        # 2. MICROESTRUCTURA DEL MERCADO
        # Eficiencia del precio
        df['price_efficiency'] = abs(df['close'] - df['open']) / (df['high'] - df['low'] + 1e-8)

        # Presión compradora/vendedora
        df['buying_pressure'] = (df['close'] - df['low']) / (df['high'] - df['low'] + 1e-8)
        df['selling_pressure'] = (df['high'] - df['close']) / (df['high'] - df['low'] + 1e-8)
        df['order_flow_imbalance'] = df['buying_pressure'] - df['selling_pressure']

        # Volatilidad realizada
        df['realized_volatility_5'] = df['returns'].rolling(5).std()
        df['realized_volatility_10'] = df['returns'].rolling(10).std()
        df['volatility_change'] = df['realized_volatility_5'] / df['realized_volatility_10'].shift(1)

        # 3. INDICADORES TÉCNICOS RÁPIDOS
        # RSI rápido
        df['rsi_3'] = talib.RSI(df['close'], timeperiod=3)
        df['rsi_5'] = talib.RSI(df['close'], timeperiod=5)
        df['rsi_change'] = df['rsi_5'] - df['rsi_5'].shift(1)

        # Stochastic rápido
        df['stoch_k_fast'], df['stoch_d_fast'] = talib.STOCHF(
            df['high'], df['low'], df['close'],
            fastk_period=5, fastd_period=3
        )

        # Williams %R rápido
        df['williams_r_5'] = talib.WILLR(df['high'], df['low'], df['close'], timeperiod=5)

        # 4. VOLUMEN Y LIQUIDEZ
        # Volume profile
        df['volume_mean_5'] = df['volume'].rolling(5).mean()
        df['volume_mean_20'] = df['volume'].rolling(20).mean()
        df['relative_volume'] = df['volume'] / df['volume_mean_20']
        df['volume_trend'] = df['volume_mean_5'] / df['volume_mean_20']

        # VWAP aproximado
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        df['vwap'] = (df['typical_price'] * df['volume']).rolling(20).sum() / df['volume'].rolling(20).sum()
        df['price_to_vwap'] = df['close'] / df['vwap']

        # 5. PATRONES Y ANOMALÍAS
        # Gaps
        df['gap'] = df['open'] / df['close'].shift(1) - 1
        df['gap_filled'] = ((df['low'] <= df['close'].shift(1)) & (df['gap'] > 0)) | \
                           ((df['high'] >= df['close'].shift(1)) & (df['gap'] < 0))

        # Breakouts
        df['close_vs_high_5'] = df['close'] / df['high'].rolling(5).max()
        df['close_vs_low_5'] = df['close'] / df['low'].rolling(5).min()
        df['potential_breakout'] = (df['close_vs_high_5'] > 0.98).astype(int)
        df['potential_breakdown'] = (df['close_vs_low_5'] < 1.02).astype(int)

        return df

    @staticmethod
    def engineer_interaction_features(df):
        """Crea features de interacción entre indicadores"""
        logger.log("🔗 Creando features de interacción...")

        # RSI vs Price momentum
        df['rsi_momentum_divergence'] = (
            np.sign(df['rsi_5'] - df['rsi_5'].shift(3)) !=
            np.sign(df['momentum_3'])
        ).astype(int)

        # Volume vs Price relationship
        df['volume_price_correlation_10'] = df['returns'].rolling(10).corr(df['volume'].pct_change())

        # Volatility regime vs Momentum
        df['high_vol_momentum'] = df['momentum_3'] * (df['realized_volatility_5'] > df['realized_volatility_5'].quantile(0.75))
        df['low_vol_momentum'] = df['momentum_3'] * (df['realized_volatility_5'] < df['realized_volatility_5'].quantile(0.25))

        # Market efficiency vs Volume
        df['efficiency_volume_interaction'] = df['price_efficiency'] * df['relative_volume']

        # 7. FEATURES DE INTERACCIÓN
        # Confluencia de señales
        df['bullish_confluence'] = (
            (df['rsi_5'] < 30).astype(int) +
            (df['stoch_k_fast'] < 20).astype(int) +
            (df['price_to_vwap'] < 0.995).astype(int) +
            (df['distance_from_mean_10'] < -0.02).astype(int)
        )

        df['bearish_confluence'] = (
            (df['rsi_5'] > 70).astype(int) +
            (df['stoch_k_fast'] > 80).astype(int) +
            (df['price_to_vwap'] > 1.005).astype(int) +
            (df['distance_from_mean_10'] > 0.02).astype(int)
        )

        # 8. ESTADÍSTICAS DE RETORNOS RECIENTES
        # Skewness y kurtosis de retornos
        df['returns_skew_10'] = df['log_returns'].rolling(10).skew()
        df['returns_kurt_10'] = df['log_returns'].rolling(10).kurt()

        # Runs test proxy (cambios de dirección)
        df['direction_changes'] = (np.sign(df['log_returns']) != np.sign(df['log_returns'].shift(1))).astype(int)
        df['direction_changes_sum_5'] = df['direction_changes'].rolling(5).sum()

        # NUEVOS FEATURES PREDICTIVOS v4.3 - MEJORAS DE PRECISIÓN
        # Features de momentum avanzado
        df['momentum_acceleration'] = df['momentum_3'].diff()
        df['rsi_momentum'] = df['rsi_5'].diff()
        df['volume_acceleration'] = df['relative_volume'].diff()

        # Features de volatilidad predictiva
        df['volatility_momentum'] = df['realized_volatility_5'].diff()
        df['volatility_percentile'] = df['realized_volatility_5'].rolling(50).rank(pct=True)

        # Features de precio predictivos
        df['price_position_in_range'] = (df['close'] - df['low'].rolling(20).min()) / (df['high'].rolling(20).max() - df['low'].rolling(20).min() + 1e-8)
        df['price_velocity'] = df['close'].pct_change(3)
        df['price_acceleration'] = df['price_velocity'].diff()

        # Features de volumen predictivos
        df['volume_price_trend'] = df['relative_volume'] * df['close'].pct_change()
        df['volume_breakout'] = (df['relative_volume'] > df['relative_volume'].rolling(20).quantile(0.8)).astype(int)

        # Features de tendencia predictivos
        df['trend_strength'] = abs(df['sma_20'] - df['sma_50']) / df['close']
        df['trend_consistency'] = (df['close'] > df['sma_20']).rolling(10).sum() / 10

        # Features de confluencia mejorados
        df['technical_confluence'] = (
            (df['rsi_5'] < 30).astype(int) +
            (df['stoch_k_fast'] < 20).astype(int) +
            (df['price_to_vwap'] < 0.995).astype(int) +
            (df['distance_from_mean_10'] < -0.02).astype(int) +
            (df['volume_breakout'] == 1).astype(int)
        )

        return df

    @staticmethod
    def select_features_by_importance(df, target_col='target_ml', top_n=30):
        """Selecciona features basado en múltiples métricas de importancia"""
        logger.log("🎯 Seleccionando mejores features por importancia...")

        try:
            from sklearn.feature_selection import mutual_info_classif
        except ImportError:
            logger.log("⚠️ sklearn.feature_selection no disponible", "WARNING")
            return df.columns.tolist()[:top_n]

        # Preparar datos
        feature_cols = [col for col in df.columns if col not in [
            'open', 'high', 'low', 'close', 'volume', 'timestamp',
            'target', 'target_ml', 'future_return'
        ]]

        X = df[feature_cols].fillna(0)
        y = df[target_col]

        # Remover filas con NaN en target
        mask = ~y.isna()
        X = X[mask]
        y = y[mask]

        if len(X) < 100:
            return feature_cols[:top_n]

        scores = {}

        # 1. Mutual Information
        try:
            mi_scores = mutual_info_classif(X, y, random_state=42)
            for i, col in enumerate(feature_cols):
                scores[col] = scores.get(col, 0) + mi_scores[i]
        except:
            pass

        # 2. Random Forest Feature Importance
        try:
            rf = RandomForestClassifier(n_estimators=50, random_state=42, n_jobs=-1)
            rf.fit(X, y)
            for i, col in enumerate(feature_cols):
                scores[col] = scores.get(col, 0) + rf.feature_importances_[i] * 100
        except:
            pass

        # 3. Correlation with target (absolute value)
        try:
            for col in feature_cols:
                corr = abs(X[col].corr(y))
                if not np.isnan(corr):
                    scores[col] = scores.get(col, 0) + corr * 10
        except:
            pass

        # Seleccionar top features
        if scores:
            sorted_features = sorted(scores.items(), key=lambda x: x[1], reverse=True)
            selected = [feat[0] for feat in sorted_features[:top_n]]

            logger.log(f"✅ Top 10 features seleccionados:")
            for i, (feat, score) in enumerate(sorted_features[:10]):
                logger.log(f"  {i+1}. {feat}: {score:.3f}")

            return selected

        return feature_cols[:top_n]

    @staticmethod
    def create_features(df):
        """Pipeline completo de features optimizado para predicción"""
        logger.log("🔧 Creando features optimizados para predicción...")

        # Features básicos existentes
        df = FeatureEngineer.add_price_features(df)
        df = FeatureEngineer.add_technical_indicators(df)
        df = FeatureEngineer.add_pattern_features(df)
        df = FeatureEngineer.add_market_structure_features(df)

        # NUEVOS: Features predictivos optimizados
        df = FeatureEngineer.add_predictive_features(df)
        df = FeatureEngineer.engineer_interaction_features(df)

        # Limpiar NaN e infinitos
        df = df.replace([np.inf, -np.inf], np.nan)
        df = df.fillna(method='ffill').fillna(0)

        # NOTA: La selección de features se hace después en prepare_data()
        # cuando ya tenemos los targets creados

        logger.log(f"✅ Features creados: {len(df.columns)} columnas")
        return df

    @staticmethod
    def select_best_features(df, target_col, max_features=50):
        """Selección automática de mejores features"""
        if not FEATURE_SELECTION_ENABLED:
            return df

        logger.log("🎯 Seleccionando mejores features...")

        # Excluir columnas no-feature
        exclude_cols = ['open', 'high', 'low', 'close', 'volume', 'timestamp',
                       target_col, 'future_return', 'target', 'target_ml']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        if len(feature_cols) <= max_features:
            return df

        # Calcular correlación con el target
        correlations = {}
        for col in feature_cols:
            try:
                corr = abs(df[col].corr(df[target_col]))
                if not np.isnan(corr):
                    correlations[col] = corr
            except:
                pass

        # Seleccionar top features
        top_features = sorted(correlations.items(), key=lambda x: x[1], reverse=True)[:max_features]
        selected_features = [feat[0] for feat in top_features]

        # Mantener columnas esenciales
        essential_cols = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
        final_cols = essential_cols + selected_features + [target_col, 'future_return', 'target', 'target_ml']
        final_cols = [col for col in final_cols if col in df.columns]

        logger.log(f"📈 Seleccionados {len(selected_features)} mejores features de {len(feature_cols)}")

        return df[final_cols]

class RobustSignalGenerator:
    """Generador de señales ultra robusto con ML avanzado y validación temporal"""

    def __init__(self):
        self.models = {}
        self.ensemble = None
        self.stacking_ensemble = None
        self.scaler = RobustScaler()  # Más robusto a outliers
        self.feature_cols = None
        self.class_weights = None
        self.model_weights = {}
        self.validation_scores = {}
        self.validator = ModelValidator()
        self.feature_selector = AdvancedFeatureSelector()
        self.model_performance_tracker = {}
        self.last_training_time = None
        self.training_data_hash = None

    def prepare_data(self, df, lookback=LOOKBACK):
        """Prepara datos para ML con targets balanceados y realistas"""
        logger.log("📊 Preparando datos robustos para ML...")

        # CAMBIO 1: Usar retornos logarítmicos para mejor normalidad
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))

        # CAMBIO 2: Horizonte de predicción más realista (siguiente vela)
        df['future_return'] = df['close'].pct_change(1).shift(-1)  # Predecir SIGUIENTE vela

        # CAMBIO 3: Umbrales basados en percentiles para balance automático
        # Esto garantiza ~33% de cada clase
        returns_clean = df['future_return'].dropna()

        # Calcular percentiles 33 y 67 para dividir en tercios iguales
        lower_percentile = returns_clean.quantile(0.33)
        upper_percentile = returns_clean.quantile(0.67)

        logger.log(f"📊 Percentiles calculados: P33={lower_percentile:.4f}, P67={upper_percentile:.4f}")

        # CAMBIO 4: Crear targets balanceados
        df['target'] = np.where(
            df['future_return'] < lower_percentile, -1,  # SHORT (33%)
            np.where(df['future_return'] > upper_percentile, 1,  # LONG (33%)
                    0)  # NEUTRAL (33%)
        )

        # Para ML necesitamos 0, 1, 2
        df['target_ml'] = df['target'] + 1

        # CAMBIO 5: Añadir features de contexto de mercado
        df['market_regime'] = self._calculate_market_regime(df)
        df['volatility_regime'] = self._calculate_volatility_regime(df)

        # CAMBIO 6: Feature engineering específico para el horizonte
        df = self._add_microstructure_features(df)

        # CAMBIO 7: Selección automática de mejores features
        if FEATURE_SELECTION_ENABLED:
            try:
                selected_features = FeatureEngineer.select_features_by_importance(df, top_n=30)
                # Mantener columnas esenciales
                essential_cols = ['open', 'high', 'low', 'close', 'volume', 'timestamp',
                                'target', 'target_ml', 'future_return', 'log_returns',
                                'market_regime', 'volatility_regime']
                cols_to_keep = essential_cols + selected_features
                cols_to_keep = [col for col in cols_to_keep if col in df.columns]
                df = df[cols_to_keep]
                logger.log(f"✅ Features seleccionados: {len(df.columns)} columnas")
            except Exception as e:
                logger.log(f"⚠️ Error en selección de features: {str(e)}", "WARNING")

        # Continuar con el proceso normal...
        # Seleccionar features finales para el modelo
        exclude_cols = ['open', 'high', 'low', 'close', 'volume', 'future_return', 'target', 'target_ml', 'timestamp']
        self.feature_cols = [col for col in df.columns if col not in exclude_cols]

        # Eliminar filas con NaN
        df_clean = df.dropna()

        if len(df_clean) < lookback + 50:  # Más datos requeridos
            logger.log("⚠️ No hay suficientes datos para entrenamiento robusto", "WARNING")
            return None, None

        X = df_clean[self.feature_cols].values
        y = df_clean['target_ml'].values.ravel()

        # Remover outliers si está habilitado
        if OUTLIER_DETECTION_ENABLED:
            X, y = self.feature_selector.remove_outliers(X, y, contamination=0.05)

        # Validar distribución de clases mejorada
        unique, counts = np.unique(y, return_counts=True)
        class_distribution = dict(zip(unique, counts))

        logger.log("📊 Distribución de clases en datos limpios:")
        total_samples = len(y)
        for val, count in zip(unique, counts):
            label = {0: "SHORT", 1: "NEUTRAL", 2: "LONG"}.get(val, "UNKNOWN")
            percentage = count/total_samples*100
            logger.log(f"  • {label}: {count} ({percentage:.1f}%)")

        # Verificar balance de clases más estricto
        min_class_size = max(20, len(y) * 0.08)  # Mínimo 8% o 20 ejemplos
        class_balance_ok = True
        for class_val, count in class_distribution.items():
            if count < min_class_size:
                logger.log(f"⚠️ Clase {class_val} insuficiente ({count} < {min_class_size})", "WARNING")
                class_balance_ok = False

        if not class_balance_ok:
            logger.log("🔄 Aplicando técnicas de balanceo de clases...")
            # Aquí podrías implementar SMOTE u otras técnicas de balanceo

        # Calcular hash de datos para detectar cambios
        import hashlib
        data_str = f"{X.shape}_{y.shape}_{np.sum(X)}_{np.sum(y)}"
        self.training_data_hash = hashlib.md5(data_str.encode()).hexdigest()

        logger.log(f"✅ Datos preparados: {X.shape[0]} muestras, {X.shape[1]} features")
        logger.log(f"🎯 Features seleccionados: {len(self.feature_cols)}")

        return X, y

    def _calculate_market_regime(self, df):
        """Identifica el régimen de mercado actual"""
        # Tendencia de corto plazo
        sma_short = df['close'].rolling(10).mean()
        sma_long = df['close'].rolling(50).mean()

        # Clasificar régimen
        regime = np.where(
            sma_short > sma_long * 1.02, 2,  # Bull market fuerte
            np.where(sma_short < sma_long * 0.98, 0,  # Bear market fuerte
                    1)  # Mercado lateral
        )
        return regime

    def _calculate_volatility_regime(self, df):
        """Identifica el régimen de volatilidad"""
        vol = df['log_returns'].rolling(20).std()
        vol_percentile = vol.rolling(100).rank(pct=True)

        # Clasificar volatilidad
        vol_regime = pd.cut(vol_percentile, bins=[0, 0.33, 0.67, 1],
                            labels=[0, 1, 2], include_lowest=True)
        return vol_regime.fillna(1).astype(int)

    def _add_microstructure_features(self, df):
        """Features específicos para predicción de corto plazo"""
        # Microestructura del mercado
        df['bid_ask_proxy'] = (df['high'] - df['low']) / df['close']
        df['price_efficiency'] = abs(df['close'] - df['open']) / (df['high'] - df['low'] + 1e-8)

        # Order flow proxy
        df['buying_pressure'] = (df['close'] - df['low']) / (df['high'] - df['low'] + 1e-8)
        df['selling_pressure'] = (df['high'] - df['close']) / (df['high'] - df['low'] + 1e-8)

        # Momentum de muy corto plazo
        df['micro_momentum'] = df['close'].pct_change(3)
        df['micro_acceleration'] = df['micro_momentum'] - df['micro_momentum'].shift(1)

        return df

    def train_models(self, X_train, y_train):
        """Entrena ensemble con modelos diversos y anti-sesgo"""
        logger.log("🤖 Entrenando ensemble anti-sesgo v5.0...")

        # IMPORTANTE: Verificar balance de clases
        unique, counts = np.unique(y_train, return_counts=True)
        logger.log(f"📊 Balance de clases en entrenamiento: {dict(zip(unique, counts))}")

        # Si hay desbalance severo, aplicar SMOTE
        try:
            from imblearn.over_sampling import SMOTE
            if min(counts) / max(counts) < 0.5:  # Si la clase minoritaria es < 50% de la mayoritaria
                logger.log("🔄 Aplicando SMOTE para balancear clases...")
                smote = SMOTE(random_state=42, k_neighbors=3)
                X_train, y_train = smote.fit_resample(X_train, y_train)
        except ImportError:
            logger.log("⚠️ SMOTE no disponible - continuando sin balanceo", "WARNING")

        # Calcular pesos de clases para balance
        classes = np.unique(y_train)
        weights = compute_class_weight('balanced', classes=classes, y=y_train.ravel())
        self.class_weights = dict(zip(classes, weights))

        logger.log(f"⚖️ Pesos de clases calculados: {self.class_weights}")

        # MODELOS DIVERSOS CON DIFERENTES SESGOS INDUCTIVOS - MEJORADOS ANTI-SESGO
        models = {
            # Modelo 1: Random Forest menos conservador para más diversidad
            'rf_conservative': RandomForestClassifier(
                n_estimators=150,  # Aumentado para mejor estabilidad
                max_depth=5,  # Menos restrictivo para capturar más patrones
                min_samples_split=20,  # Menos restrictivo
                min_samples_leaf=10,  # Menos restrictivo
                max_features='sqrt',
                class_weight='balanced_subsample',
                random_state=42,
                n_jobs=-1
            ),

            # Modelo 2: XGBoost menos regularizado para más diversidad
            'xgb_regularized': XGBClassifier(
                n_estimators=100,  # Aumentado
                max_depth=4,  # Menos restrictivo
                learning_rate=0.01,
                reg_alpha=5.0,  # L1 muy alto
                reg_lambda=5.0,  # L2 muy alto
                subsample=0.5,
                colsample_bytree=0.5,
                scale_pos_weight=1,  # Se ajustará dinámicamente
                random_state=42,
                eval_metric='mlogloss',
                verbosity=0
            ),

            # Modelo 3: Logistic Regression con diferentes regularizaciones
            'lr_l1': LogisticRegression(
                penalty='l1',
                C=0.1,
                solver='liblinear',
                class_weight='balanced',
                random_state=42
            ),

            'lr_l2': LogisticRegression(
                penalty='l2',
                C=0.1,
                solver='lbfgs',
                class_weight='balanced',
                max_iter=1000,
                random_state=42
            ),

            # Modelo 4: SVM con diferentes kernels para capturar patrones no lineales
            'svm_linear': SVC(
                kernel='linear',
                C=0.1,
                class_weight='balanced',
                probability=True,
                random_state=42
            ),

            'svm_rbf': SVC(
                kernel='rbf',
                C=1.0,
                gamma='scale',
                class_weight='balanced',
                probability=True,
                random_state=42
            ),

            # Modelo 5: Naive Bayes (naturalmente resistente al overfitting)
            'naive_bayes': GaussianNB(
                var_smoothing=1e-9
            ),

            # Modelo 6: K-Nearest Neighbors
            'knn': KNeighborsClassifier(
                n_neighbors=15,  # Muchos vecinos para suavizar
                weights='distance',
                metric='manhattan'
            ),

            # Modelo 7: Extra Trees menos restrictivo para más diversidad
            'extra_trees': ExtraTreesClassifier(
                n_estimators=100,  # Aumentado
                max_depth=6,  # Menos restrictivo
                min_samples_split=15,  # Menos restrictivo
                min_samples_leaf=5,  # Menos restrictivo
                max_features='log2',
                class_weight='balanced',
                random_state=42,
                n_jobs=-1
            ),

            # Modelo 8: Gradient Boosting menos conservador
            'gb_conservative': GradientBoostingClassifier(
                n_estimators=80,  # Aumentado
                max_depth=4,  # Menos restrictivo
                learning_rate=0.1,  # Menos lento
                subsample=0.8,  # Más datos
                min_samples_split=20,  # Menos restrictivo
                random_state=42
            ),

            # Modelo 9: Decision Tree menos restrictivo para más diversidad
            'dt_pruned': DecisionTreeClassifier(
                max_depth=6,  # Menos restrictivo
                min_samples_split=20,  # Menos restrictivo
                min_samples_leaf=10,  # Menos restrictivo
                max_features='sqrt',
                class_weight='balanced',
                random_state=42
            ),

            # Modelo 10: LightGBM con dart (reduce overfitting)
            'lgb_dart': lgb.LGBMClassifier(
                boosting_type='dart',
                n_estimators=100,  # Aumentado
                max_depth=5,  # Menos restrictivo
                learning_rate=0.1,  # Menos lento
                num_leaves=15,  # Más hojas
                subsample=0.8,  # Más datos
                colsample_bytree=0.8,  # Más features
                reg_alpha=1.0,  # Menos regularización
                reg_lambda=1.0,  # Menos regularización
                min_child_samples=20,  # Menos restrictivo
                drop_rate=0.1,
                class_weight='balanced',
                random_state=42,
                verbosity=-1
            ),

            # Modelo 11: Logistic Regression con regularización Ridge
            'lr_ridge': LogisticRegression(
                penalty='l2',
                C=0.1,  # Alta regularización (equivalente a alpha=10)
                solver='lbfgs',
                class_weight='balanced',
                max_iter=1000,
                random_state=42
            ),

            # Modelo 12: MLP simple (modelo no lineal simple)
            'mlp_simple': MLPClassifier(
                hidden_layer_sizes=(10,),  # Una sola capa pequeña
                alpha=1.0,  # Alta regularización
                learning_rate_init=0.01,
                max_iter=500,
                early_stopping=True,
                validation_fraction=0.2,
                n_iter_no_change=10,
                random_state=42
            )
        }

        # Ajustar scale_pos_weight para XGBoost basado en el balance de clases
        if 'xgb_regularized' in models:
            scale_pos_weight = counts[0] / counts[2] if len(counts) > 2 else 1
            models['xgb_regularized'].set_params(scale_pos_weight=scale_pos_weight)

        # IMPORTANTE: Validación especial para detectar sesgo
        validated_models = {}
        for name, model in models.items():
            try:
                logger.log(f"  • Entrenando {name}...")

                # Entrenar modelo
                model.fit(X_train, y_train)

                # Predecir en train para verificar sesgo
                train_pred = model.predict(X_train)
                train_unique, train_counts = np.unique(train_pred, return_counts=True)

                # Verificar que el modelo predice todas las clases
                if len(train_unique) < 3:
                    logger.log(f"  ❌ {name}: Modelo sesgado - solo predice {len(train_unique)} clases", "WARNING")
                    continue

                # Verificar que ninguna clase domina más del 60%
                max_class_ratio = max(train_counts) / len(train_pred)
                if max_class_ratio > 0.6:
                    logger.log(f"  ⚠️ {name}: Posible sesgo - {max_class_ratio:.1%} en una clase", "WARNING")

                # Validación temporal normal
                val_result = self.validator.temporal_cross_validation(X_train, y_train, model)

                if val_result and val_result['mean'] > 0.35:  # Umbral más realista
                    validated_models[name] = model
                    self.validation_scores[name] = val_result['mean']
                    logger.log(f"  ✅ {name}: Val={val_result['mean']:.3f}")

            except Exception as e:
                logger.log(f"  ❌ Error en {name}: {str(e)}", "ERROR")

        self.models = validated_models

        # Crear ensemble con votación ponderada por diversidad
        if len(self.models) >= 3:
            self._create_diverse_ensemble(X_train, y_train)
        else:
            logger.log("❌ No hay suficientes modelos robustos para ensemble", "ERROR")

        # Guardar modelos si está habilitado
        if MODEL_PERSISTENCE_ENABLED:
            self._save_models()

        # Marcar tiempo de entrenamiento
        self.last_training_time = datetime.datetime.now()

    def _create_diverse_ensemble(self, X_train, y_train):
        """Crea ensemble que premia la diversidad de predicciones"""
        logger.log("🎭 Creando ensemble diverso...")

        # Calcular matriz de diversidad entre modelos
        predictions = {}
        for name, model in self.models.items():
            predictions[name] = model.predict(X_train)

        # Calcular diversidad por pares
        diversity_scores = {}
        for name1 in self.models:
            diversity_scores[name1] = 0
            for name2 in self.models:
                if name1 != name2:
                    # Disagreement rate
                    disagreement = np.mean(predictions[name1] != predictions[name2])
                    diversity_scores[name1] += disagreement

        # Normalizar scores de diversidad
        max_diversity = max(diversity_scores.values()) if diversity_scores else 1
        for name in diversity_scores:
            diversity_scores[name] /= max_diversity

        # Combinar con accuracy para pesos finales
        for name in self.models:
            accuracy_weight = self.validation_scores.get(name, 0.5)
            diversity_weight = diversity_scores.get(name, 0.5)
            # 70% accuracy, 30% diversidad
            self.model_weights[name] = 0.7 * accuracy_weight + 0.3 * diversity_weight

        # Normalizar pesos
        total_weight = sum(self.model_weights.values())
        for name in self.model_weights:
            self.model_weights[name] /= total_weight

        logger.log("📊 Pesos del ensemble diverso:")
        for name, weight in self.model_weights.items():
            logger.log(f"  • {name}: {weight:.3f}")

        # Crear ensemble - IMPORTANTE: Solo usar modelos que realmente existen
        estimators = [(name, model) for name, model in self.models.items()]

        # Filtrar pesos para que coincidan exactamente con los modelos disponibles
        filtered_weights = [self.model_weights[name] for name, _ in estimators if name in self.model_weights]

        # Verificar que el número de pesos coincida con el número de estimadores
        if len(filtered_weights) != len(estimators):
            logger.log(f"⚠️ Ajustando pesos: {len(estimators)} modelos, {len(filtered_weights)} pesos", "WARNING")
            filtered_weights = None  # Usar pesos uniformes si no coinciden

        self.ensemble = VotingClassifier(
            estimators=estimators,
            voting='soft',
            weights=filtered_weights
        )
        self.ensemble.fit(X_train, y_train)

        ensemble_score = self.ensemble.score(X_train, y_train)
        logger.log(f"✅ Ensemble diverso creado - Score: {ensemble_score:.3f}")
        logger.log(f"🎯 Modelos validados: {len(self.models)}")

    def _create_stacking_ensemble(self, X_train, y_train):
        """Crea ensemble de stacking avanzado"""
        try:
            logger.log("🏗️ Creando Stacking Ensemble...")

            # Seleccionar mejores modelos base para stacking
            base_models = list(self.models.items())[:5]  # Top 5 modelos

            # Meta-learner más simple para evitar overfitting
            meta_learner = LogisticRegression(
                C=0.1,  # Más regularización
                random_state=42,
                class_weight=self.class_weights
            )

            self.stacking_ensemble = StackingClassifier(
                estimators=base_models,
                final_estimator=meta_learner,
                cv=3,  # Validación cruzada para meta-features
                n_jobs=-1
            )

            self.stacking_ensemble.fit(X_train, y_train)

            stacking_score = self.stacking_ensemble.score(X_train, y_train)
            logger.log(f"✅ Stacking Ensemble creado - Score: {stacking_score:.3f}")

        except Exception as e:
            logger.log(f"⚠️ Error creando Stacking Ensemble: {str(e)}", "WARNING")
            self.stacking_ensemble = None

    def _log_model_performance_summary(self):
        """Muestra resumen detallado de performance de modelos"""
        logger.log("\n📊 RESUMEN DE PERFORMANCE DE MODELOS:")
        logger.log("="*50)

        for name, metrics in self.model_performance_tracker.items():
            logger.log(f"{name}:")
            logger.log(f"  • Train Score: {metrics.get('train_score', 0):.3f}")
            logger.log(f"  • Val Score: {metrics.get('val_score', 0):.3f} ± {metrics.get('val_std', 0):.3f}")
            logger.log(f"  • Estabilidad: {metrics.get('stability', 0):.3f}")
            logger.log(f"  • Overfitting: {'❌' if metrics.get('overfitting', False) else '✅'}")

        logger.log("="*50)

    def _save_models(self):
        """Guarda modelos entrenados para persistencia"""
        try:
            model_data = {
                'models': self.models,
                'ensemble': self.ensemble,
                'stacking_ensemble': self.stacking_ensemble,
                'scaler': self.scaler,
                'feature_cols': self.feature_cols,
                'class_weights': self.class_weights,
                'model_weights': self.model_weights,
                'validation_scores': self.validation_scores,
                'training_time': self.last_training_time,
                'data_hash': self.training_data_hash
            }

            joblib.dump(model_data, 'modelos/robust_models_v4.pkl')
            logger.log("💾 Modelos guardados exitosamente")

        except Exception as e:
            logger.log(f"⚠️ Error guardando modelos: {str(e)}", "WARNING")

    def _generate_technical_signals(self, df):
        """Genera señales basadas solo en análisis técnico cuando no hay modelos ML"""
        try:
            logger.log("📈 Generando señales con análisis técnico puro...")

            if len(df) < 50:
                logger.log("⚠️ Datos insuficientes para análisis técnico", "WARNING")
                return pd.DataFrame()

            # Obtener últimos valores
            latest = df.iloc[-1]

            # Análisis técnico básico
            rsi = latest.get('rsi', 50)
            macd = latest.get('macd', 0)
            adx = latest.get('adx', 20)
            price = latest.get('close', 0)
            sma_20 = latest.get('sma_20', price)
            sma_50 = latest.get('sma_50', price)

            # Lógica de señales técnicas conservadoras
            signal = 0  # Neutral por defecto
            confidence = 0.0

            # Condiciones para LONG
            if (rsi < 40 and macd > 0 and price > sma_20 and sma_20 > sma_50 and adx > 25):
                signal = 1  # LONG
                confidence = 0.45  # Baja confianza sin ML

            # Condiciones para SHORT
            elif (rsi > 60 and macd < 0 and price < sma_20 and sma_20 < sma_50 and adx > 25):
                signal = -1  # SHORT
                confidence = 0.45  # Baja confianza sin ML

            # MEJORA v4.3: Umbral más estricto para análisis técnico
            if confidence < 0.60:  # Umbral más alto para mayor precisión
                logger.log("📊 Análisis técnico: Condiciones no favorables")
                logger.log(f"   RSI: {rsi:.1f}, MACD: {macd:.1f}, ADX: {adx:.1f}")
                logger.log("❌ No hay señales técnicas de calidad suficiente")
                return pd.DataFrame()

            # Crear señal técnica
            signals = pd.DataFrame({
                'timestamp': [df.index[-1]],
                'signal': [signal],
                'confidence': [confidence],
                'ensemble_confidence': [confidence],
                'consensus_score': [1.0],  # 100% consenso (solo análisis técnico)
                'price': [price],
                'atr': [latest.get('atr', price * 0.01)],
                'quality_score': [1.0],
                'model_count': [0]  # Sin modelos ML
            })

            signal_type = {-1: "SHORT", 0: "NEUTRAL", 1: "LONG"}[signal]
            logger.log(f"📊 Señal técnica generada: {signal_type}")
            logger.log(f"🎯 Confianza técnica: {confidence:.1%}")
            logger.log(f"⚠️ NOTA: Señal basada solo en análisis técnico (sin ML)")

            return signals

        except Exception as e:
            logger.log(f"❌ Error en análisis técnico: {str(e)}", "ERROR")
            return pd.DataFrame()

    def generate_signals(self, df):
        """Genera señales con validación anti-sesgo y calibración"""
        if not self.models or not self.ensemble:
            logger.log("🔍 Modo análisis técnico - Sin modelos ML", "INFO")
            return self._generate_technical_signals(df)

        X, _ = self.prepare_data(df)
        if X is None:
            return pd.DataFrame()

        try:
            # PASO 1: Obtener predicciones de todos los modelos
            all_predictions = []
            all_probabilities = []
            model_predictions = {}

            for name, model in self.models.items():
                try:
                    pred = model.predict(X)
                    prob = model.predict_proba(X)

                    # Guardar para análisis
                    model_predictions[name] = {
                        'predictions': pred,
                        'probabilities': prob,
                        'distribution': np.bincount(pred, minlength=3) / len(pred)
                    }

                    all_predictions.append(pred)
                    all_probabilities.append(prob)

                except Exception as e:
                    logger.log(f"⚠️ Error en {name}: {str(e)}", "WARNING")

            # PASO 2: Análisis de sesgo por modelo
            logger.log("\n📊 ANÁLISIS DE SESGO POR MODELO:")
            biased_models = []

            for name, data in model_predictions.items():
                dist = data['distribution']
                max_class_ratio = np.max(dist)
                dominant_class = np.argmax(dist)

                logger.log(f"{name}: SHORT={dist[0]:.1%}, NEUTRAL={dist[1]:.1%}, LONG={dist[2]:.1%}")

                # MEJORA v4.4: Detección de sesgo más permisiva para mayor diversidad
                if max_class_ratio > 0.55:  # REDUCIDO: Si una clase tiene más del 55% (antes 70%)
                    biased_models.append(name)
                    logger.log(f"  ⚠️ SESGO DETECTADO: {max_class_ratio:.1%} en clase {dominant_class}")

                # Detectar también modelos que predicen muy pocas clases (más permisivo)
                active_classes = np.sum(dist > 0.03)  # REDUCIDO: Clases con más del 3% (antes 5%)
                if active_classes < 2:  # REDUCIDO: Al menos 2 clases activas (antes 3)
                    biased_models.append(name)
                    logger.log(f"  ⚠️ MODELO LIMITADO: Solo {active_classes} clases activas")

            # PASO 3: Crear ensemble ponderado anti-sesgo
            if len(biased_models) > len(self.models) * 0.5:
                logger.log("\n⚠️ Mayoría de modelos sesgados - aplicando corrección")
                ensemble_predictions, ensemble_probabilities = self._weighted_ensemble_prediction(
                    model_predictions, X, anti_bias=True
                )
            else:
                # Usar ensemble normal
                ensemble_predictions = self.ensemble.predict(X)
                ensemble_probabilities = self.ensemble.predict_proba(X)

            # PASO 4: Calibración de probabilidades
            calibrated_probabilities = self._calibrate_probabilities(ensemble_probabilities)

            # PASO 5: Aplicar threshold dinámico basado en distribución
            signals = self._apply_dynamic_thresholds(
                ensemble_predictions,
                calibrated_probabilities,
                df
            )

            # PASO 6: Validación final anti-sesgo
            if not signals.empty:
                signals = self._validate_signals_diversity(signals)

            return signals

        except Exception as e:
            logger.log(f"❌ Error generando señales: {str(e)}", "ERROR")
            return pd.DataFrame()

    def _weighted_ensemble_prediction(self, model_predictions, X, anti_bias=True):
        """Ensemble ponderado que penaliza modelos sesgados"""
        n_samples = X.shape[0]
        n_classes = 3

        # Inicializar matriz de probabilidades
        weighted_probs = np.zeros((n_samples, n_classes))

        for name, data in model_predictions.items():
            # Calcular peso basado en diversidad de predicciones
            dist = data['distribution']
            entropy = -np.sum(dist * np.log(dist + 1e-10))  # Entropía
            max_entropy = -np.log(1/n_classes)  # Entropía máxima
            diversity_weight = entropy / max_entropy  # 0 a 1

            # Penalizar modelos muy sesgados
            if anti_bias:
                max_class_ratio = np.max(dist)
                bias_penalty = 1.0 - max_class_ratio
                final_weight = diversity_weight * bias_penalty
            else:
                final_weight = diversity_weight

            # Aplicar peso a las probabilidades
            weighted_probs += data['probabilities'] * final_weight

            logger.log(f"  {name}: peso={final_weight:.3f} (div={diversity_weight:.3f})")

        # Normalizar probabilidades
        weighted_probs /= np.sum(weighted_probs, axis=1, keepdims=True)

        # Obtener predicciones finales
        weighted_predictions = np.argmax(weighted_probs, axis=1)

        return weighted_predictions, weighted_probs

    def _calibrate_probabilities(self, probabilities):
        """Calibra probabilidades usando temperature scaling"""
        # Temperature scaling simple
        temperature = 1.5  # > 1 suaviza las probabilidades

        # Aplicar temperatura
        log_probs = np.log(probabilities + 1e-10)
        scaled_log_probs = log_probs / temperature

        # Softmax para obtener probabilidades calibradas
        exp_probs = np.exp(scaled_log_probs - np.max(scaled_log_probs, axis=1, keepdims=True))
        calibrated = exp_probs / np.sum(exp_probs, axis=1, keepdims=True)

        return calibrated

    def _apply_dynamic_thresholds(self, predictions, probabilities, df):
        """Aplica thresholds dinámicos basados en condiciones del mercado"""
        # Calcular confianza máxima por predicción
        max_probs = np.max(probabilities, axis=1)

        # Obtener volatilidad actual
        current_volatility = df['volatility'].iloc[-1] if 'volatility' in df else 0.01

        # Ajustar threshold basado en volatilidad
        base_threshold = MIN_CONFIDENCE
        volatility_multiplier = 1 + (current_volatility / 0.02)  # Aumenta con volatilidad
        dynamic_threshold = base_threshold * volatility_multiplier

        logger.log(f"🎯 Threshold dinámico: {dynamic_threshold:.3f} (vol={current_volatility:.3f})")

        # Filtrar por confianza
        confidence_mask = max_probs >= dynamic_threshold

        # Mapear predicciones
        signal_map = {0: -1, 1: 0, 2: 1}  # SHORT, NEUTRAL, LONG
        mapped_signals = np.array([signal_map.get(int(p), 0) for p in predictions])

        # Aplicar máscara de confianza
        filtered_signals = np.where(confidence_mask, mapped_signals, 0)

        # Crear DataFrame de señales
        timestamps = df.index[-len(predictions):]
        prices = df['close'].iloc[-len(predictions):].values
        atr_values = df['atr'].iloc[-len(predictions):].values if 'atr' in df else prices * 0.005

        signals_df = pd.DataFrame({
            'timestamp': timestamps,
            'signal': filtered_signals,
            'confidence': max_probs,
            'price': prices,
            'atr': atr_values,
            'prob_short': probabilities[:, 0],
            'prob_neutral': probabilities[:, 1],
            'prob_long': probabilities[:, 2],
            'consensus_score': max_probs,  # Para compatibilidad
            'ensemble_confidence': max_probs  # Para compatibilidad
        })

        # Filtrar solo señales no neutrales
        signals_df = signals_df[signals_df['signal'] != 0]

        # Tomar solo la señal más reciente
        if len(signals_df) > 0:
            return signals_df.tail(1)

        return pd.DataFrame()

    def _validate_signals_diversity(self, signals):
        """Validación final para asegurar diversidad en señales"""
        if signals.empty:
            return signals

        # Analizar distribución de señales en ventana reciente
        recent_signals = []
        if hasattr(self, 'signal_history'):
            recent_signals = self.signal_history[-20:]  # Últimas 20 señales

        # Añadir señal actual
        current_signal = signals.iloc[-1]['signal']
        recent_signals.append(current_signal)

        # Calcular sesgo en señales recientes
        if len(recent_signals) >= 5:
            signal_counts = pd.Series(recent_signals).value_counts()
            total_signals = len(recent_signals)

            # Si hay demasiado sesgo hacia un lado
            for signal_type, count in signal_counts.items():
                if count / total_signals > 0.8:  # Más del 80% del mismo tipo
                    logger.log(f"⚠️ Sesgo detectado en señales recientes: {count}/{total_signals} son {signal_type}")

                    # Aumentar threshold de confianza para esta señal
                    if signals.iloc[-1]['signal'] == signal_type:
                        min_confidence_required = 0.5  # Requiere 50% confianza
                        if signals.iloc[-1]['confidence'] < min_confidence_required:
                            logger.log(f"🚫 Señal rechazada por sesgo histórico (conf={signals.iloc[-1]['confidence']:.1%})")
                            return pd.DataFrame()

        # Guardar historial
        if not hasattr(self, 'signal_history'):
            self.signal_history = []
        self.signal_history = recent_signals[-50:]  # Mantener últimas 50

        # Log de probabilidades detalladas
        if not signals.empty:
            signal = signals.iloc[-1]
            logger.log(f"\n📊 PROBABILIDADES DETALLADAS:")
            logger.log(f"  • SHORT: {signal['prob_short']:.1%}")
            logger.log(f"  • NEUTRAL: {signal['prob_neutral']:.1%}")
            logger.log(f"  • LONG: {signal['prob_long']:.1%}")
            logger.log(f"  • Señal elegida: {'LONG' if signal['signal'] == 1 else 'SHORT'}")
            logger.log(f"  • Confianza: {signal['confidence']:.1%}")

        return signals

class RiskManager:
    """Gestión de riesgo profesional"""

    def __init__(self, initial_capital=INITIAL_CAPITAL):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = []
        self.daily_pnl = 0
        self.trade_history = []

    def calculate_position_size(self, signal_confidence):
        """Calcula tamaño de posición basado en Kelly Criterion modificado"""
        # Kelly conservador
        kelly_fraction = (signal_confidence - 0.5) / 0.5
        kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Máximo 25% Kelly

        # Ajustar por capital actual
        position_size = self.current_capital * kelly_fraction * MAX_RISK_PER_TRADE

        # Establecer un tamaño mínimo de posición si la señal es válida
        min_position = max(50, self.current_capital * 0.005)  # $50 o 0.5% del capital
        if position_size < min_position and signal_confidence >= MIN_CONFIDENCE:
            position_size = min_position

        # Límites de seguridad
        max_position = self.current_capital * 0.1  # Máximo 10% del capital
        position_size = min(position_size, max_position)

        return position_size

    def calculate_stop_loss(self, entry_price, atr, direction):
        """Stop loss dinámico mejorado basado en ATR"""
        atr_multiplier = ATR_MULTIPLIER_SL  # Usar configuración global

        if direction == 1:  # LONG
            stop_loss = entry_price - (atr * atr_multiplier)
        else:  # SHORT
            stop_loss = entry_price + (atr * atr_multiplier)

        return stop_loss

    def calculate_take_profit(self, entry_price, atr, direction):
        """Take profit optimizado con ratio riesgo/beneficio mejorado"""
        tp_multiplier = ATR_MULTIPLIER_TP  # Usar configuración global
        tp_distance = atr * tp_multiplier

        if direction == 1:  # LONG
            take_profit = entry_price + tp_distance
        else:  # SHORT
            take_profit = entry_price - tp_distance

        return take_profit

    def calculate_trailing_stop(self, entry_price, current_price, direction, atr, best_price=None):
        """Trailing stop loss dinámico"""
        if not TRAILING_STOP_ENABLED:
            return self.calculate_stop_loss(entry_price, atr, direction)

        if best_price is None:
            best_price = current_price

        trailing_distance = atr * (ATR_MULTIPLIER_SL * 0.8)  # Más ajustado que SL inicial

        if direction == 1:  # LONG
            # Solo mover el stop loss hacia arriba
            trailing_stop = best_price - trailing_distance
            initial_stop = self.calculate_stop_loss(entry_price, atr, direction)
            return max(trailing_stop, initial_stop)
        else:  # SHORT
            # Solo mover el stop loss hacia abajo
            trailing_stop = best_price + trailing_distance
            initial_stop = self.calculate_stop_loss(entry_price, atr, direction)
            return min(trailing_stop, initial_stop)

    def is_stop_loss_hit(self, current_price, stop_loss, direction):
        """Detección robusta de stop loss con tolerancia"""
        tolerance = TP_SL_TOLERANCE

        if direction == 1:  # LONG
            # Para LONG, SL se activa si precio <= stop_loss (con tolerancia)
            return current_price <= (stop_loss * (1 + tolerance))
        else:  # SHORT
            # Para SHORT, SL se activa si precio >= stop_loss (con tolerancia)
            return current_price >= (stop_loss * (1 - tolerance))

    def is_take_profit_hit(self, current_price, take_profit, direction):
        """Detección robusta de take profit con tolerancia"""
        tolerance = TP_SL_TOLERANCE

        if direction == 1:  # LONG
            # Para LONG, TP se activa si precio >= take_profit (con tolerancia)
            return current_price >= (take_profit * (1 - tolerance))
        else:  # SHORT
            # Para SHORT, TP se activa si precio <= take_profit (con tolerancia)
            return current_price <= (take_profit * (1 + tolerance))

    def check_daily_loss_limit(self):
        """Verifica límite de pérdida diaria"""
        daily_loss_pct = abs(self.daily_pnl / self.initial_capital)

        if daily_loss_pct >= MAX_DAILY_LOSS:
            logger.log(f"🛑 Límite diario alcanzado: {daily_loss_pct:.2%}", "WARNING")
            return True
        return False

    def update_capital(self, pnl):
        """Actualiza capital y estadísticas"""
        self.current_capital += pnl
        self.daily_pnl += pnl

        # Log estado
        logger.log(f"💰 Capital: ${self.current_capital:.2f} | PnL Diario: ${self.daily_pnl:.2f}")

class SimulatedTrader:
    """Trading en modo simulación"""

    def __init__(self):
        self.data_manager = DataManager()
        self.feature_engineer = FeatureEngineer()
        self.signal_generator = RobustSignalGenerator()
        self.risk_manager = RiskManager()
        self.active_positions = []

    def train_system(self, df):
        """Entrena el sistema con datos históricos"""
        # Crear features
        df_features = self.feature_engineer.create_features(df.copy())

        # Preparar datos
        X, y = self.signal_generator.prepare_data(df_features)

        if X is not None and len(X) > 100:
            # Split 80/20
            split_idx = int(0.8 * len(X))
            X_train, y_train = X[:split_idx], y[:split_idx]

            # Escalar datos
            X_train_scaled = self.signal_generator.scaler.fit_transform(X_train)

            # Entrenar modelos
            self.signal_generator.train_models(X_train_scaled, y_train)

            logger.log("✅ Sistema entrenado y listo")
            logger.log(f"📊 Modelos activos: {list(self.signal_generator.models.keys())}")
            return True

        return False

    def execute_trade(self, signal, current_df, current_price=None):
        """Ejecuta trade en simulación con información completa"""
        # Calcular parámetros
        position_size = self.risk_manager.calculate_position_size(signal['confidence'])

        # Obtener ATR actual del DataFrame
        if 'atr' in current_df.columns and not current_df['atr'].empty:
            current_atr = current_df['atr'].iloc[-1]
            if pd.isna(current_atr) or current_atr <= 0:
                current_atr = current_df['close'].iloc[-1] * 0.005
        else:
            current_atr = current_df['close'].iloc[-1] * 0.005

        # Usar el precio actual real para la entrada
        entry_price = current_price if current_price is not None else current_df['close'].iloc[-1]

        stop_loss = self.risk_manager.calculate_stop_loss(
            entry_price, current_atr, signal['signal']
        )

        take_profit = self.risk_manager.calculate_take_profit(
            entry_price, current_atr, signal['signal']
        )

        trade = {
            'timestamp': signal['timestamp'],
            'type': 'LONG' if signal['signal'] == 1 else 'SHORT',
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'position_size': position_size,
            'confidence': signal['confidence'],
            'status': 'OPEN',
            'entry_time': datetime.datetime.now()
        }

        self.active_positions.append(trade)

        # Log detallado
        logger.log(f"\n{'='*60}")
        logger.log(f"🎯 NUEVA POSICIÓN ABIERTA:")
        logger.log(f"├─ Tipo: {'🟢 LONG' if trade['type'] == 'LONG' else '🔴 SHORT'}")
        logger.log(f"├─ Entrada: ${trade['entry_price']:.2f}")
        logger.log(f"├─ Stop Loss: ${stop_loss:.2f} ({abs(stop_loss-trade['entry_price'])/trade['entry_price']*100:.2f}%)")
        logger.log(f"├─ Take Profit: ${take_profit:.2f} ({abs(take_profit-trade['entry_price'])/trade['entry_price']*100:.2f}%)")
        logger.log(f"├─ Tamaño: ${position_size:.2f}")
        logger.log(f"├─ Confianza: {trade['confidence']:.2%}")
        logger.log(f"└─ Risk/Reward: 1:{abs(take_profit-trade['entry_price'])/abs(stop_loss-trade['entry_price']):.1f}")
        logger.log(f"{'='*60}\n")

        return trade

    def update_positions(self, current_price, current_atr=None):
        """Actualiza posiciones abiertas con detección robusta de TP/SL SIN trailing"""
        for position in self.active_positions:
            if position['status'] != 'OPEN':
                continue

            direction = 1 if position['type'] == 'LONG' else -1

            # Actualizar mejor precio para estadísticas (sin afectar TP/SL)
            if 'best_price' not in position:
                position['best_price'] = position['entry_price']

            # Actualizar mejor precio solo para tracking
            if position['type'] == 'LONG' and current_price > position['best_price']:
                position['best_price'] = current_price
            elif position['type'] == 'SHORT' and current_price < position['best_price']:
                position['best_price'] = current_price

            # NO TRAILING STOP/PROFIT - Mantener niveles originales
            # Los niveles de TP/SL se mantienen fijos desde la entrada

            # Calcular distancias para alertas
            if position['type'] == 'LONG':
                dist_to_sl = (current_price - position['stop_loss']) / position['stop_loss'] * 100
                dist_to_tp = (position['take_profit'] - current_price) / current_price * 100

                # Alertas de proximidad mejoradas
                if 0 < dist_to_sl < 1.0:  # Dentro del 1%
                    logger.log(f"🚨 ALERTA CRÍTICA: LONG muy cerca de SL! Distancia: {dist_to_sl:.3f}%")
                elif 0 < dist_to_tp < 1.0:  # Dentro del 1%
                    logger.log(f"🎯 ALERTA: LONG cerca de TP! Distancia: {dist_to_tp:.3f}%")

            else:  # SHORT
                dist_to_sl = (position['stop_loss'] - current_price) / current_price * 100
                dist_to_tp = (current_price - position['take_profit']) / position['take_profit'] * 100

                # Alertas de proximidad mejoradas
                if 0 < dist_to_sl < 1.0:  # Dentro del 1%
                    logger.log(f"🚨 ALERTA CRÍTICA: SHORT muy cerca de SL! Distancia: {dist_to_sl:.3f}%")
                elif 0 < dist_to_tp < 1.0:  # Dentro del 1%
                    logger.log(f"🎯 ALERTA: SHORT cerca de TP! Distancia: {dist_to_tp:.3f}%")

            # Usar detección robusta de TP/SL
            if self.risk_manager.is_stop_loss_hit(current_price, position['stop_loss'], direction):
                logger.log(f"🛑 STOP LOSS ACTIVADO: {position['type']} @ ${current_price:.2f}")
                self.close_position(position, current_price, 'STOP_LOSS')
            elif self.risk_manager.is_take_profit_hit(current_price, position['take_profit'], direction):
                logger.log(f"🎯 TAKE PROFIT ACTIVADO: {position['type']} @ ${current_price:.2f}")
                self.close_position(position, current_price, 'TAKE_PROFIT')

    def close_position(self, position, exit_price, reason):
        """Cierra posición y calcula PnL con detalles"""
        if position['type'] == 'LONG':
            pnl = (exit_price - position['entry_price']) / position['entry_price'] * position['position_size']
            pnl_pct = (exit_price - position['entry_price']) / position['entry_price'] * 100
        else:  # SHORT
            pnl = (position['entry_price'] - exit_price) / position['entry_price'] * position['position_size']
            pnl_pct = (position['entry_price'] - exit_price) / position['entry_price'] * 100

        position['exit_price'] = exit_price
        position['pnl'] = pnl
        position['pnl_pct'] = pnl_pct
        position['status'] = 'CLOSED'
        position['exit_reason'] = reason
        position['exit_time'] = datetime.datetime.now()
        position['duration'] = (position['exit_time'] - position['entry_time']).seconds // 60

        # Actualizar capital
        self.risk_manager.update_capital(pnl)

        # Log detallado del resultado
        emoji = "💰" if pnl > 0 else "📉"
        logger.log(f"\n{emoji} POSICIÓN CERRADA:")
        logger.log(f"├─ Tipo: {position['type']}")
        logger.log(f"├─ Entrada: ${position['entry_price']:.2f}")
        logger.log(f"├─ Salida: ${exit_price:.2f}")
        logger.log(f"├─ Razón: {reason}")
        logger.log(f"├─ PnL: ${pnl:.2f} ({pnl_pct:.2f}%)")
        logger.log(f"├─ Duración: {position['duration']} minutos")
        logger.log(f"└─ Capital actualizado: ${self.risk_manager.current_capital:.2f}")

        # Si es el primer trade cerrado, mostrar información adicional
        closed_count = len([p for p in self.active_positions if p['status'] == 'CLOSED'])
        if closed_count == 1:
            logger.log(f"\n🎉 ¡PRIMER TRADE COMPLETADO!")
            logger.log(f"💡 El sistema está funcionando correctamente.")
            logger.log(f"📊 Continúa monitoreando para ver más trades...")

    def generate_report(self):
        """Genera reporte de rendimiento"""
        closed_positions = [p for p in self.active_positions if p['status'] == 'CLOSED']

        # Calcular métricas básicas
        current_capital = self.risk_manager.current_capital
        initial_capital = self.risk_manager.initial_capital
        roi = (current_capital - initial_capital) / initial_capital * 100

        if not closed_positions:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'total_pnl': 0,
                'sharpe_ratio': 0,
                'current_capital': current_capital,
                'roi': roi
            }

        # Calcular métricas
        wins = [p for p in closed_positions if p['pnl'] > 0]
        losses = [p for p in closed_positions if p['pnl'] < 0]

        win_rate = len(wins) / len(closed_positions) * 100

        total_wins = sum(p['pnl'] for p in wins) if wins else 0
        total_losses = abs(sum(p['pnl'] for p in losses)) if losses else 1

        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')

        # Calcular Sharpe Ratio
        returns = [p['pnl'] / p['position_size'] for p in closed_positions]
        if len(returns) > 1:
            sharpe = np.mean(returns) / (np.std(returns) + 1e-8) * np.sqrt(252)
        else:
            sharpe = 0

        report = {
            'total_trades': len(closed_positions),
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_pnl': sum(p['pnl'] for p in closed_positions),
            'sharpe_ratio': sharpe,
            'current_capital': current_capital,
            'roi': roi
        }

        return report

class ParameterOptimizer:
    """Optimizador de parámetros con grid search y validación cruzada temporal"""

    def __init__(self, trader):
        self.trader = trader
        self.best_params = {}
        self.optimization_results = {}

    def optimize_parameters(self, df, optimization_periods=3):
        """Optimización completa de parámetros con validación temporal"""
        logger.log("🚀 INICIANDO OPTIMIZACIÓN AGRESIVA DE PARÁMETROS")
        logger.log("="*60)

        # Definir grid de parámetros optimizado (más eficiente)
        param_grid = {
            'min_confidence': [0.25, 0.35, 0.45],
            'ensemble_agreement': [0.55, 0.65, 0.75],
            'atr_sl_multiplier': [1.8, 2.2, 2.8],
            'atr_tp_multiplier': [3.5, 4.5, 5.5],
            'bias_threshold': [0.68, 0.72, 0.76]
        }

        # Calcular total de combinaciones
        total_combinations = 1
        for key in param_grid:
            total_combinations *= len(param_grid[key])

        logger.log(f"📊 Grid Search: {total_combinations} combinaciones a evaluar")
        logger.log("⚡ Optimización rápida activada - evaluando combinaciones clave...")

        best_score = -float('inf')
        best_combination = None
        current_combination = 0

        # Grid search optimizado
        for min_conf in param_grid['min_confidence']:
            for ensemble_agr in param_grid['ensemble_agreement']:
                for atr_sl in param_grid['atr_sl_multiplier']:
                    for atr_tp in param_grid['atr_tp_multiplier']:
                        for bias_thresh in param_grid['bias_threshold']:
                            current_combination += 1

                            # Mostrar progreso cada 50 combinaciones o cada 25%
                            if current_combination % 50 == 0 or current_combination % max(1, total_combinations // 4) == 0:
                                progress = (current_combination / total_combinations) * 100
                                logger.log(f"🔄 Progreso: {progress:.1f}% ({current_combination}/{total_combinations})")

                            # Configurar parámetros temporales
                            temp_params = {
                                'min_confidence': min_conf,
                                'ensemble_agreement': ensemble_agr,
                                'atr_sl_multiplier': atr_sl,
                                'atr_tp_multiplier': atr_tp,
                                'bias_threshold': bias_thresh
                            }

                            # Evaluación rápida
                            score = self._quick_evaluation(df, temp_params)

                            # Guardar resultados
                            self.optimization_results[str(temp_params)] = {
                                'params': temp_params,
                                'score': score
                            }

                            # Actualizar mejor combinación
                            if score > best_score:
                                best_score = score
                                best_combination = temp_params.copy()
                                logger.log(f"🏆 Nuevo mejor score: {best_score:.4f}")
                                logger.log(f"   Parámetros: conf={min_conf:.0%}, cons={ensemble_agr:.0%}, sl={atr_sl:.1f}, tp={atr_tp:.1f}")

        self.best_params = best_combination
        logger.log("✅ Optimización completada")
        logger.log(f"🏆 Mejor score final: {best_score:.4f}")
        logger.log(f"🎯 Mejores parámetros encontrados:")
        for key, value in best_combination.items():
            logger.log(f"   {key}: {value}")

        return best_combination

    def _quick_evaluation(self, df, params):
        """Evaluación rápida de parámetros usando simulación directa"""
        try:
            # Aplicar parámetros temporalmente
            original_params = self._backup_current_params()
            self._apply_temp_params(params)

            # Usar datos suficientes pero evaluar de forma más simple
            if len(df) < 500:
                return 0  # Datos insuficientes

            # Usar últimos 400 datos para evaluación rápida
            df_eval = df.tail(400).copy()

            # Simular trades sin re-entrenar modelos (usar modelos existentes)
            wins = 0
            total_trades = 0
            total_pnl = 0

            try:
                # Evaluar cada 10 períodos para velocidad
                for i in range(100, len(df_eval), 10):
                    current_data = df_eval.iloc[:i+1]

                    # Simular generación de señal simplificada
                    if len(current_data) < 50:
                        continue

                    # Usar datos recientes para simular señal
                    recent_data = current_data.tail(50)
                    current_price = recent_data.iloc[-1]['close']

                    # Simular señal basada en parámetros
                    confidence = self._simulate_confidence(recent_data, params)

                    if confidence >= params['min_confidence']:
                        # Simular dirección (simplificado)
                        direction = 1 if recent_data['close'].pct_change().tail(5).mean() > 0 else -1

                        # Simular trade
                        future_data = df_eval.iloc[i:]
                        if len(future_data) > 1:
                            result = self._simulate_quick_trade_simple(direction, current_price, future_data, params)
                            if result:
                                total_trades += 1
                                if result['pnl'] > 0:
                                    wins += 1
                                total_pnl += result['pnl']

                # Calcular score
                if total_trades == 0:
                    return 0

                win_rate = wins / total_trades
                avg_pnl = total_pnl / total_trades

                # Score combinado: win_rate * 0.7 + normalized_pnl * 0.3
                normalized_pnl = max(-0.5, min(0.5, avg_pnl / (current_price * 0.005)))
                score = win_rate * 0.7 + (normalized_pnl + 0.5) * 0.3

                return score

            except Exception as e:
                return 0
            finally:
                # Restaurar parámetros originales
                self._restore_params(original_params)

        except Exception as e:
            return 0

    def _simulate_confidence(self, data, params):
        """Simula confianza basada en volatilidad y momentum"""
        try:
            if len(data) < 10:
                return 0

            # Calcular indicadores simples
            returns = data['close'].pct_change().dropna()
            volatility = returns.std()
            momentum = returns.tail(5).mean()

            # Simular confianza basada en condiciones del mercado
            base_confidence = 0.3

            # Ajustar por volatilidad
            if volatility > 0.02:  # Alta volatilidad
                base_confidence += 0.2

            # Ajustar por momentum
            if abs(momentum) > 0.001:  # Momentum claro
                base_confidence += 0.2

            return min(0.9, base_confidence)

        except:
            return 0.3

    def _simulate_quick_trade_simple(self, direction, entry_price, future_data, params):
        """Simulación muy rápida de trade"""
        try:
            if len(future_data) < 2:
                return None

            # Calcular ATR simplificado
            high_low = future_data['high'] - future_data['low']
            atr = high_low.tail(10).mean()

            # Calcular SL y TP
            sl_multiplier = params['atr_sl_multiplier']
            tp_multiplier = params['atr_tp_multiplier']

            if direction == 1:  # LONG
                stop_loss = entry_price - (atr * sl_multiplier)
                take_profit = entry_price + (atr * tp_multiplier)
            else:  # SHORT
                stop_loss = entry_price + (atr * sl_multiplier)
                take_profit = entry_price - (atr * tp_multiplier)

            # Evaluar hasta 20 períodos
            max_periods = min(20, len(future_data))

            for i in range(1, max_periods):
                current_price = future_data.iloc[i]['close']

                # Verificar TP/SL
                if direction == 1:  # LONG
                    if current_price >= take_profit:
                        return {'pnl': take_profit - entry_price, 'exit': 'TP'}
                    elif current_price <= stop_loss:
                        return {'pnl': stop_loss - entry_price, 'exit': 'SL'}
                else:  # SHORT
                    if current_price <= take_profit:
                        return {'pnl': entry_price - take_profit, 'exit': 'TP'}
                    elif current_price >= stop_loss:
                        return {'pnl': entry_price - stop_loss, 'exit': 'SL'}

            # Salida por tiempo
            final_price = future_data.iloc[max_periods-1]['close']
            if direction == 1:
                pnl = final_price - entry_price
            else:
                pnl = entry_price - final_price

            return {'pnl': pnl, 'exit': 'TIME'}

        except:
            return None

    def _backup_current_params(self):
        """Respalda parámetros actuales"""
        return {
            'MIN_CONFIDENCE': globals().get('MIN_CONFIDENCE', 0.45),
            'ENSEMBLE_MIN_AGREEMENT': globals().get('ENSEMBLE_MIN_AGREEMENT', 0.70),
            'ATR_MULTIPLIER_SL': globals().get('ATR_MULTIPLIER_SL', 2.0),
            'ATR_MULTIPLIER_TP': globals().get('ATR_MULTIPLIER_TP', 5.0)
        }

    def _apply_temp_params(self, params):
        """Aplica parámetros temporales"""
        globals()['MIN_CONFIDENCE'] = params['min_confidence']
        globals()['ENSEMBLE_MIN_AGREEMENT'] = params['ensemble_agreement']
        globals()['ATR_MULTIPLIER_SL'] = params['atr_sl_multiplier']
        globals()['ATR_MULTIPLIER_TP'] = params['atr_tp_multiplier']

    def _restore_params(self, original_params):
        """Restaura parámetros originales"""
        for key, value in original_params.items():
            globals()[key] = value

    def _simulate_quick_trade(self, signal, entry_price, future_data):
        """Simulación rápida de trade para optimización"""
        try:
            if len(future_data) < 2:
                return None

            direction = signal['signal']
            atr = signal.get('atr', entry_price * 0.01)

            # Calcular SL y TP
            sl_multiplier = globals().get('ATR_MULTIPLIER_SL', 2.0)
            tp_multiplier = globals().get('ATR_MULTIPLIER_TP', 5.0)

            if direction == 1:  # LONG
                stop_loss = entry_price - (atr * sl_multiplier)
                take_profit = entry_price + (atr * tp_multiplier)
            else:  # SHORT
                stop_loss = entry_price + (atr * sl_multiplier)
                take_profit = entry_price - (atr * tp_multiplier)

            # Simular hasta 12 períodos (12 horas)
            max_periods = min(12, len(future_data))

            for i in range(1, max_periods):
                current_price = future_data.iloc[i]['close']

                # Verificar TP/SL
                if direction == 1:  # LONG
                    if current_price >= take_profit:
                        pnl = take_profit - entry_price
                        return {'pnl': pnl, 'exit_reason': 'TP'}
                    elif current_price <= stop_loss:
                        pnl = stop_loss - entry_price
                        return {'pnl': pnl, 'exit_reason': 'SL'}
                else:  # SHORT
                    if current_price <= take_profit:
                        pnl = entry_price - take_profit
                        return {'pnl': pnl, 'exit_reason': 'TP'}
                    elif current_price >= stop_loss:
                        pnl = entry_price - stop_loss
                        return {'pnl': pnl, 'exit_reason': 'SL'}

            # Salida por tiempo
            final_price = future_data.iloc[max_periods-1]['close']
            if direction == 1:
                pnl = final_price - entry_price
            else:
                pnl = entry_price - final_price

            return {'pnl': pnl, 'exit_reason': 'TIME'}

        except Exception as e:
            return None

    def apply_best_parameters(self):
        """Aplica los mejores parámetros encontrados"""
        if self.best_params:
            logger.log("🎯 Aplicando mejores parámetros encontrados...")
            self._apply_temp_params(self.best_params)
            logger.log("✅ Parámetros optimizados aplicados")
            return True
        return False

    def display_optimization_summary(self):
        """Muestra resumen de la optimización"""
        if not self.optimization_results:
            return

        logger.log("\n🏆 RESUMEN DE OPTIMIZACIÓN")
        logger.log("="*50)

        # Ordenar por score
        sorted_results = sorted(
            self.optimization_results.values(),
            key=lambda x: x['score'],
            reverse=True
        )

        # Mostrar top 5
        logger.log("🥇 TOP 5 MEJORES COMBINACIONES:")
        for i, result in enumerate(sorted_results[:5]):
            params = result['params']
            logger.log(f"\n#{i+1} - Score: {result['score']:.4f}")
            logger.log(f"  🎯 Confianza: {params['min_confidence']:.0%}")
            logger.log(f"  🤝 Consenso: {params['ensemble_agreement']:.0%}")
            logger.log(f"  🛡️ SL: {params['atr_sl_multiplier']:.1f}x ATR")
            logger.log(f"  🎯 TP: {params['atr_tp_multiplier']:.1f}x ATR")

    def optimize_for_roi(self, df, base_params=None):
        """Optimización ultra-fina enfocada en maximizar ROI"""
        logger.log("🎯 INICIANDO OPTIMIZACIÓN ULTRA-FINA PARA ROI")
        logger.log("="*60)

        # Usar parámetros base de la optimización anterior o valores por defecto
        if base_params is None:
            base_params = {
                'min_confidence': 0.25,
                'ensemble_agreement': 0.55,
                'atr_sl_multiplier': 2.8,
                'atr_tp_multiplier': 5.5,
                'bias_threshold': 0.68
            }

        logger.log(f"📊 Parámetros base: {base_params}")

        # Grid ultra-fino alrededor de los mejores parámetros
        fine_ranges = {
            'min_confidence': [
                max(0.15, base_params['min_confidence'] - 0.05),
                base_params['min_confidence'],
                base_params['min_confidence'] + 0.03,
                min(0.60, base_params['min_confidence'] + 0.08)
            ],
            'ensemble_agreement': [
                max(0.40, base_params['ensemble_agreement'] - 0.05),
                base_params['ensemble_agreement'],
                base_params['ensemble_agreement'] + 0.05,
                min(0.85, base_params['ensemble_agreement'] + 0.10)
            ],
            'atr_sl_multiplier': [
                max(1.5, base_params['atr_sl_multiplier'] - 0.4),
                max(1.8, base_params['atr_sl_multiplier'] - 0.2),
                base_params['atr_sl_multiplier'],
                min(4.0, base_params['atr_sl_multiplier'] + 0.3)
            ],
            'atr_tp_multiplier': [
                max(3.0, base_params['atr_tp_multiplier'] - 0.8),
                base_params['atr_tp_multiplier'],
                min(7.0, base_params['atr_tp_multiplier'] + 0.8),
                min(8.0, base_params['atr_tp_multiplier'] + 1.5)
            ]
        }

        total_combinations = 1
        for key in fine_ranges:
            total_combinations *= len(fine_ranges[key])

        logger.log(f"🔍 Grid ultra-fino: {total_combinations} combinaciones")
        logger.log("🎯 Optimizando específicamente para ROI positivo...")

        best_roi = -float('inf')
        best_combination = None
        best_metrics = None
        current_combination = 0

        # Evaluación ultra-fina
        for min_conf in fine_ranges['min_confidence']:
            for ensemble_agr in fine_ranges['ensemble_agreement']:
                for atr_sl in fine_ranges['atr_sl_multiplier']:
                    for atr_tp in fine_ranges['atr_tp_multiplier']:
                        current_combination += 1

                        if current_combination % 16 == 0:
                            progress = (current_combination / total_combinations) * 100
                            logger.log(f"🔄 Progreso fino: {progress:.1f}% ({current_combination}/{total_combinations})")

                        temp_params = {
                            'min_confidence': min_conf,
                            'ensemble_agreement': ensemble_agr,
                            'atr_sl_multiplier': atr_sl,
                            'atr_tp_multiplier': atr_tp,
                            'bias_threshold': base_params['bias_threshold']  # Mantener fijo
                        }

                        # Evaluación detallada enfocada en ROI
                        metrics = self._detailed_roi_evaluation(df, temp_params)

                        # Criterio de optimización: ROI > 0 y win_rate > 25%
                        roi_score = metrics['roi']
                        win_rate = metrics['win_rate']
                        profit_factor = metrics['profit_factor']

                        # Score combinado priorizando ROI positivo
                        if roi_score > 0 and win_rate > 0.25:
                            combined_score = roi_score * 3.0 + win_rate * 1.0 + max(0, profit_factor - 1.0) * 0.5
                        else:
                            combined_score = roi_score * 1.5 + win_rate * 0.5

                        if combined_score > best_roi:
                            best_roi = combined_score
                            best_combination = temp_params.copy()
                            best_metrics = metrics.copy()

                            if roi_score > 0:
                                logger.log(f"🏆 ROI POSITIVO: {roi_score:.3f} ({win_rate:.1%} WR, PF:{profit_factor:.2f})")
                                logger.log(f"   📊 Conf:{min_conf:.0%}, SL:{atr_sl:.1f}, TP:{atr_tp:.1f}")

        self.best_params = best_combination
        logger.log("✅ Optimización ultra-fina completada")

        if best_metrics and best_metrics['roi'] > 0:
            logger.log(f"🎉 ¡ROI POSITIVO CONSEGUIDO!")
            logger.log(f"🏆 ROI: {best_metrics['roi']:.3f} ({best_metrics['roi']*100:.2f}%)")
            logger.log(f"📊 Win Rate: {best_metrics['win_rate']:.1%}")
            logger.log(f"📈 Profit Factor: {best_metrics['profit_factor']:.2f}")
            logger.log(f"🎯 Trades: {best_metrics['trades']}")
        else:
            logger.log(f"⚠️ ROI aún negativo, pero mejorado: {best_metrics['roi']*100:.2f}% vs anterior")

        logger.log(f"🎯 Mejores parámetros ultra-finos:")
        for key, value in best_combination.items():
            if 'multiplier' in key:
                logger.log(f"   {key}: {value:.1f}")
            elif 'threshold' in key or 'confidence' in key or 'agreement' in key:
                logger.log(f"   {key}: {value:.0%}")
            else:
                logger.log(f"   {key}: {value}")

        return best_combination, best_metrics

    def _detailed_roi_evaluation(self, df, params):
        """Evaluación detallada enfocada en ROI real"""
        try:
            # Aplicar parámetros temporalmente
            original_params = self._backup_current_params()
            self._apply_temp_params(params)

            if len(df) < 500:
                return {'roi': -1, 'win_rate': 0, 'profit_factor': 0, 'trades': 0}

            # Usar datos más amplios para evaluación precisa
            df_eval = df.tail(700).copy()

            wins = 0
            losses = 0
            total_pnl = 0
            gross_profit = 0
            gross_loss = 0
            trades_executed = 0

            try:
                # Evaluación más detallada cada 8 períodos
                for i in range(200, len(df_eval), 8):
                    current_data = df_eval.iloc[:i+1]

                    if len(current_data) < 150:
                        continue

                    # Simular señal más realista
                    recent_data = current_data.tail(150)
                    current_price = recent_data.iloc[-1]['close']

                    # Calcular confianza más precisa
                    confidence = self._calculate_realistic_confidence(recent_data, params)

                    if confidence >= params['min_confidence']:
                        # Determinar dirección basada en múltiples factores
                        direction = self._determine_direction(recent_data)

                        # Simular trade con más precisión
                        future_data = df_eval.iloc[i:]
                        if len(future_data) > 8:
                            result = self._simulate_precise_trade(direction, current_price, future_data, params)
                            if result:
                                trades_executed += 1
                                pnl = result['pnl']
                                total_pnl += pnl

                                if pnl > 0:
                                    wins += 1
                                    gross_profit += pnl
                                else:
                                    losses += 1
                                    gross_loss += abs(pnl)

                # Calcular métricas finales
                if trades_executed == 0:
                    return {'roi': -1, 'win_rate': 0, 'profit_factor': 0, 'trades': 0}

                win_rate = wins / trades_executed
                roi = total_pnl / 10000  # Capital inicial
                profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

                return {
                    'roi': roi,
                    'win_rate': win_rate,
                    'profit_factor': profit_factor,
                    'trades': trades_executed,
                    'total_pnl': total_pnl,
                    'gross_profit': gross_profit,
                    'gross_loss': gross_loss
                }

            except Exception as e:
                return {'roi': -1, 'win_rate': 0, 'profit_factor': 0, 'trades': 0}
            finally:
                self._restore_params(original_params)

        except Exception as e:
            return {'roi': -1, 'win_rate': 0, 'profit_factor': 0, 'trades': 0}

    def _calculate_realistic_confidence(self, data, params):
        """Calcula confianza más realista basada en múltiples factores"""
        try:
            if len(data) < 20:
                return 0

            # Factores múltiples para confianza
            returns = data['close'].pct_change().dropna()
            volatility = returns.std()
            momentum = returns.tail(10).mean()

            # Confianza base
            base_confidence = 0.20

            # Ajustes por volatilidad
            if 0.01 < volatility < 0.03:  # Volatilidad óptima
                base_confidence += 0.15
            elif volatility > 0.03:  # Alta volatilidad
                base_confidence += 0.10

            # Ajustes por momentum
            if abs(momentum) > 0.002:  # Momentum fuerte
                base_confidence += 0.15

            return min(0.85, base_confidence)

        except:
            return 0.25

    def _determine_direction(self, data):
        """Determina dirección basada en múltiples indicadores"""
        try:
            if len(data) < 10:
                return 1

            # Factores para dirección
            price_momentum = data['close'].pct_change().tail(5).mean()

            # Combinar señales
            if price_momentum > 0.001:
                return 1  # LONG
            elif price_momentum < -0.001:
                return -1  # SHORT
            else:
                return 1  # Default LONG

        except:
            return 1

    def _simulate_precise_trade(self, direction, entry_price, future_data, params):
        """Simulación más precisa de trade"""
        try:
            if len(future_data) < 3:
                return None

            # Calcular ATR más preciso
            if len(future_data) >= 14:
                high_low = future_data['high'] - future_data['low']
                atr = high_low.tail(14).mean()
            else:
                atr = (future_data['high'] - future_data['low']).mean()

            # Calcular SL y TP
            sl_multiplier = params['atr_sl_multiplier']
            tp_multiplier = params['atr_tp_multiplier']

            if direction == 1:  # LONG
                stop_loss = entry_price - (atr * sl_multiplier)
                take_profit = entry_price + (atr * tp_multiplier)
            else:  # SHORT
                stop_loss = entry_price + (atr * sl_multiplier)
                take_profit = entry_price - (atr * tp_multiplier)

            # Evaluar hasta 30 períodos (30 horas)
            max_periods = min(30, len(future_data))

            for i in range(1, max_periods):
                current_price = future_data.iloc[i]['close']
                high_price = future_data.iloc[i]['high']
                low_price = future_data.iloc[i]['low']

                # Verificar TP/SL con precios high/low
                if direction == 1:  # LONG
                    if high_price >= take_profit:
                        return {'pnl': take_profit - entry_price, 'exit': 'TP', 'periods': i}
                    elif low_price <= stop_loss:
                        return {'pnl': stop_loss - entry_price, 'exit': 'SL', 'periods': i}
                else:  # SHORT
                    if low_price <= take_profit:
                        return {'pnl': entry_price - take_profit, 'exit': 'TP', 'periods': i}
                    elif high_price >= stop_loss:
                        return {'pnl': entry_price - stop_loss, 'exit': 'SL', 'periods': i}

            # Salida por tiempo
            final_price = future_data.iloc[max_periods-1]['close']
            if direction == 1:
                pnl = final_price - entry_price
            else:
                pnl = entry_price - final_price

            return {'pnl': pnl, 'exit': 'TIME', 'periods': max_periods}

        except:
            return None

    def optimize_profit_factor(self, df, target_pf=1.2):
        """Optimización específica para conseguir Profit Factor > 1.0"""
        logger.log("⚖️ INICIANDO OPTIMIZACIÓN PROFIT FACTOR > 1.0")
        logger.log("="*60)
        logger.log(f"🎯 Objetivo: Profit Factor > {target_pf}")
        logger.log("🔍 Enfoque: Reducir pérdidas promedio y optimizar R:R")

        # Grid especializado para Profit Factor
        pf_grid = {
            'min_confidence': [0.20, 0.25, 0.30, 0.35, 0.40],  # Más conservador
            'ensemble_agreement': [0.50, 0.60, 0.70, 0.80],    # Más consenso
            'atr_sl_multiplier': [1.8, 2.0, 2.2, 2.5],         # SL más ajustado
            'atr_tp_multiplier': [4.0, 5.0, 6.0, 7.0, 8.0],    # TP más amplio
            'max_loss_threshold': [0.015, 0.020, 0.025],       # Límite pérdida
        }

        total_combinations = 1
        for key in pf_grid:
            if key != 'max_loss_threshold':  # No contar este parámetro
                total_combinations *= len(pf_grid[key])
        total_combinations *= len(pf_grid['max_loss_threshold'])

        logger.log(f"📊 Grid Profit Factor: {total_combinations} combinaciones")
        logger.log("⚖️ Priorizando: PF > 1.0, luego ROI positivo...")

        best_pf = 0
        best_combination = None
        best_metrics = None
        current_combination = 0
        pf_positive_found = False

        # Evaluación enfocada en Profit Factor
        for min_conf in pf_grid['min_confidence']:
            for ensemble_agr in pf_grid['ensemble_agreement']:
                for atr_sl in pf_grid['atr_sl_multiplier']:
                    for atr_tp in pf_grid['atr_tp_multiplier']:
                        for max_loss in pf_grid['max_loss_threshold']:
                            current_combination += 1

                            if current_combination % 25 == 0:
                                progress = (current_combination / total_combinations) * 100
                                logger.log(f"🔄 Progreso PF: {progress:.1f}% ({current_combination}/{total_combinations})")

                            temp_params = {
                                'min_confidence': min_conf,
                                'ensemble_agreement': ensemble_agr,
                                'atr_sl_multiplier': atr_sl,
                                'atr_tp_multiplier': atr_tp,
                                'max_loss_threshold': max_loss,
                                'bias_threshold': 0.70  # Fijo para estabilidad
                            }

                            # Evaluación específica para Profit Factor
                            metrics = self._evaluate_profit_factor(df, temp_params)

                            profit_factor = metrics['profit_factor']
                            roi = metrics['roi']
                            win_rate = metrics['win_rate']
                            avg_loss = metrics.get('avg_loss', 0)

                            # Criterio: PF > 1.0 es prioritario
                            if profit_factor >= 1.0:
                                if not pf_positive_found:
                                    logger.log(f"🎉 ¡PRIMER PF > 1.0 ENCONTRADO!")
                                    pf_positive_found = True

                                logger.log(f"⚖️ PF: {profit_factor:.2f} | ROI: {roi:.3f} | WR: {win_rate:.1%}")
                                logger.log(f"   📊 Conf:{min_conf:.0%}, SL:{atr_sl:.1f}, TP:{atr_tp:.1f}, MaxLoss:{max_loss:.1%}")

                                # Score para PF > 1.0: priorizar PF alto y ROI positivo
                                score = profit_factor * 2.0 + max(0, roi) * 1.0 + win_rate * 0.5
                            else:
                                # Score para PF < 1.0: acercarse a 1.0
                                score = profit_factor * 1.5 + max(0, roi) * 0.5 + win_rate * 0.3

                            # Penalizar pérdidas promedio altas
                            if avg_loss < -0.008:  # Pérdidas muy altas
                                score *= 0.8

                            if score > best_pf:
                                best_pf = score
                                best_combination = temp_params.copy()
                                best_metrics = metrics.copy()

        self.best_params = best_combination
        logger.log("✅ Optimización Profit Factor completada")

        if best_metrics:
            pf = best_metrics['profit_factor']
            if pf >= 1.0:
                logger.log(f"🎉 ¡PROFIT FACTOR > 1.0 CONSEGUIDO!")
                logger.log(f"⚖️ Profit Factor: {pf:.2f}")
                logger.log(f"🏆 ROI: {best_metrics['roi']:.3f} ({best_metrics['roi']*100:.2f}%)")
                logger.log(f"📊 Win Rate: {best_metrics['win_rate']:.1%}")
                logger.log(f"💰 Ganancia promedio: ${best_metrics.get('avg_win', 0):.2f}")
                logger.log(f"💸 Pérdida promedio: ${best_metrics.get('avg_loss', 0):.2f}")
            else:
                logger.log(f"⚠️ PF aún < 1.0, pero optimizado: {pf:.2f}")
                logger.log(f"📈 Progreso hacia PF = 1.0: {(pf/1.0)*100:.1f}%")

        logger.log(f"🎯 Mejores parámetros para Profit Factor:")
        for key, value in best_combination.items():
            if 'multiplier' in key:
                logger.log(f"   {key}: {value:.1f}")
            elif 'threshold' in key or 'confidence' in key or 'agreement' in key:
                logger.log(f"   {key}: {value:.0%}")
            else:
                logger.log(f"   {key}: {value}")

        return best_combination, best_metrics

    def _evaluate_profit_factor(self, df, params):
        """Evaluación específica enfocada en Profit Factor"""
        try:
            # Aplicar parámetros temporalmente
            original_params = self._backup_current_params()
            self._apply_temp_params(params)

            if len(df) < 600:
                return {'profit_factor': 0, 'roi': -1, 'win_rate': 0, 'trades': 0}

            # Usar datos amplios para evaluación robusta
            df_eval = df.tail(800).copy()

            wins = 0
            losses = 0
            total_pnl = 0
            gross_profit = 0
            gross_loss = 0
            trades_executed = 0
            win_amounts = []
            loss_amounts = []

            try:
                # Evaluación cada 6 períodos para balance velocidad/precisión
                for i in range(250, len(df_eval), 6):
                    current_data = df_eval.iloc[:i+1]

                    if len(current_data) < 200:
                        continue

                    # Simular señal con parámetros optimizados
                    recent_data = current_data.tail(200)
                    current_price = recent_data.iloc[-1]['close']

                    # Calcular confianza con nuevo umbral
                    confidence = self._calculate_pf_confidence(recent_data, params)

                    if confidence >= params['min_confidence']:
                        # Determinar dirección
                        direction = self._determine_pf_direction(recent_data)

                        # Simular trade con gestión de pérdidas mejorada
                        future_data = df_eval.iloc[i:]
                        if len(future_data) > 10:
                            result = self._simulate_pf_trade(direction, current_price, future_data, params)
                            if result:
                                trades_executed += 1
                                pnl = result['pnl']
                                total_pnl += pnl

                                if pnl > 0:
                                    wins += 1
                                    gross_profit += pnl
                                    win_amounts.append(pnl)
                                else:
                                    losses += 1
                                    gross_loss += abs(pnl)
                                    loss_amounts.append(pnl)

                # Calcular métricas enfocadas en Profit Factor
                if trades_executed == 0:
                    return {'profit_factor': 0, 'roi': -1, 'win_rate': 0, 'trades': 0}

                win_rate = wins / trades_executed
                roi = total_pnl / 10000  # Capital inicial
                profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

                avg_win = np.mean(win_amounts) if win_amounts else 0
                avg_loss = np.mean(loss_amounts) if loss_amounts else 0

                return {
                    'profit_factor': profit_factor,
                    'roi': roi,
                    'win_rate': win_rate,
                    'trades': trades_executed,
                    'total_pnl': total_pnl,
                    'gross_profit': gross_profit,
                    'gross_loss': gross_loss,
                    'avg_win': avg_win,
                    'avg_loss': avg_loss,
                    'wins': wins,
                    'losses': losses
                }

            except Exception as e:
                return {'profit_factor': 0, 'roi': -1, 'win_rate': 0, 'trades': 0}
            finally:
                self._restore_params(original_params)

        except Exception as e:
            return {'profit_factor': 0, 'roi': -1, 'win_rate': 0, 'trades': 0}

    def _calculate_pf_confidence(self, data, params):
        """Calcula confianza optimizada para Profit Factor"""
        try:
            if len(data) < 30:
                return 0

            # Factores más conservadores para PF
            returns = data['close'].pct_change().dropna()
            volatility = returns.std()
            momentum = returns.tail(15).mean()  # Momentum más largo
            trend_strength = abs(momentum) / volatility if volatility > 0 else 0

            # Confianza base más conservadora
            base_confidence = 0.15

            # Ajustes más estrictos
            if 0.008 < volatility < 0.025:  # Volatilidad óptima más estrecha
                base_confidence += 0.12
            elif volatility > 0.025:  # Penalizar alta volatilidad
                base_confidence += 0.05

            # Momentum más estricto
            if abs(momentum) > 0.003:  # Momentum más fuerte requerido
                base_confidence += 0.15

            # Factor de fuerza de tendencia
            if trend_strength > 0.2:  # Tendencia clara
                base_confidence += 0.10

            # Límite de pérdida máxima
            max_loss_threshold = params.get('max_loss_threshold', 0.02)
            recent_max_loss = abs(returns.tail(10).min())
            if recent_max_loss > max_loss_threshold:
                base_confidence *= 0.7  # Reducir confianza si hay pérdidas grandes recientes

            return min(0.80, base_confidence)

        except:
            return 0.20

    def _determine_pf_direction(self, data):
        """Determina dirección optimizada para Profit Factor"""
        try:
            if len(data) < 15:
                return 1

            # Análisis más robusto para PF
            short_momentum = data['close'].pct_change().tail(5).mean()
            medium_momentum = data['close'].pct_change().tail(15).mean()

            # Confirmación de tendencia
            if short_momentum > 0.002 and medium_momentum > 0.001:
                return 1  # LONG fuerte
            elif short_momentum < -0.002 and medium_momentum < -0.001:
                return -1  # SHORT fuerte
            elif abs(short_momentum) > abs(medium_momentum) * 1.5:
                return 1 if short_momentum > 0 else -1  # Momentum dominante
            else:
                return 1  # Default conservador

        except:
            return 1

    def _simulate_pf_trade(self, direction, entry_price, future_data, params):
        """Simulación de trade optimizada para Profit Factor"""
        try:
            if len(future_data) < 5:
                return None

            # Calcular ATR más conservador
            if len(future_data) >= 20:
                high_low = future_data['high'] - future_data['low']
                atr = high_low.tail(20).mean()  # ATR más estable
            else:
                atr = (future_data['high'] - future_data['low']).mean()

            # Parámetros optimizados para PF
            sl_multiplier = params['atr_sl_multiplier']
            tp_multiplier = params['atr_tp_multiplier']
            max_loss_threshold = params.get('max_loss_threshold', 0.02)

            # Calcular SL y TP más conservadores
            if direction == 1:  # LONG
                stop_loss = entry_price - (atr * sl_multiplier)
                take_profit = entry_price + (atr * tp_multiplier)

                # Límite de pérdida máxima
                max_loss_price = entry_price * (1 - max_loss_threshold)
                stop_loss = max(stop_loss, max_loss_price)

            else:  # SHORT
                stop_loss = entry_price + (atr * sl_multiplier)
                take_profit = entry_price - (atr * tp_multiplier)

                # Límite de pérdida máxima
                max_loss_price = entry_price * (1 + max_loss_threshold)
                stop_loss = min(stop_loss, max_loss_price)

            # Evaluar hasta 35 períodos (más tiempo para TP)
            max_periods = min(35, len(future_data))

            for i in range(1, max_periods):
                high_price = future_data.iloc[i]['high']
                low_price = future_data.iloc[i]['low']
                close_price = future_data.iloc[i]['close']

                # Verificar TP/SL con precios intraday
                if direction == 1:  # LONG
                    if high_price >= take_profit:
                        pnl = take_profit - entry_price
                        return {'pnl': pnl, 'exit': 'TP', 'periods': i}
                    elif low_price <= stop_loss:
                        pnl = stop_loss - entry_price
                        return {'pnl': pnl, 'exit': 'SL', 'periods': i}
                else:  # SHORT
                    if low_price <= take_profit:
                        pnl = entry_price - take_profit
                        return {'pnl': pnl, 'exit': 'TP', 'periods': i}
                    elif high_price >= stop_loss:
                        pnl = entry_price - stop_loss
                        return {'pnl': pnl, 'exit': 'SL', 'periods': i}

                # Verificar límite de pérdida adicional en el close
                if direction == 1:
                    current_loss = (entry_price - close_price) / entry_price
                else:
                    current_loss = (close_price - entry_price) / entry_price

                if current_loss > max_loss_threshold * 1.2:  # 20% margen adicional
                    pnl = entry_price - close_price if direction == 1 else close_price - entry_price
                    return {'pnl': pnl, 'exit': 'MAX_LOSS', 'periods': i}

            # Salida por tiempo con gestión conservadora
            final_price = future_data.iloc[max_periods-1]['close']
            if direction == 1:
                pnl = final_price - entry_price
            else:
                pnl = entry_price - final_price

            # Limitar pérdidas por tiempo también
            max_time_loss = entry_price * max_loss_threshold * 0.8
            if abs(pnl) > max_time_loss and pnl < 0:
                pnl = -max_time_loss

            return {'pnl': pnl, 'exit': 'TIME', 'periods': max_periods}

        except:
            return None

class LiveTradingBot:
    """Bot principal para trading en vivo (simulado)"""

    def __init__(self):
        self.trader = SimulatedTrader()
        self.is_trained = False
        self.last_signal_time = None
        self.timeframes = ['15m', '1h']
        self.total_trades_executed = 0
        self.optimizer = ParameterOptimizer(self.trader)

    def show_market_summary(self, df):
        """Muestra resumen rápido del mercado"""
        current_price = df['close'].iloc[-1]
        price_change = (current_price - df['close'].iloc[-5]) / df['close'].iloc[-5] * 100
        rsi = talib.RSI(df['close'])[-1]
        volume_avg = df['volume'].rolling(20).mean().iloc[-1]
        volume_ratio = df['volume'].iloc[-1] / volume_avg

        # Obtener símbolo del activo
        asset_symbol = SYMBOL.split('/')[0]

        logger.log(f"\n💹 MERCADO {asset_symbol}: ${current_price:.2f} ({price_change:+.2f}%) | "
                  f"RSI: {rsi:.0f} {'🔴' if rsi > 70 else '🟢' if rsi < 30 else '⚪'} | "
                  f"Vol: {'🔥' if volume_ratio > 1.5 else '📊'} x{volume_ratio:.1f}")

    def initialize(self):
        """Inicializa el bot con datos históricos"""
        logger.log("🚀 Inicializando Trading Bot ULTRA MEJORADO v4.1")
        logger.log(f"💱 Activo seleccionado: {SYMBOL}")
        logger.log("📚 Descargando datos históricos para entrenamiento optimizado...")
        logger.log("⏰ Usando timeframe de 1 hora para mayor estabilidad...")

        # Obtener MUCHOS más datos históricos con timeframe más largo
        df = self.trader.data_manager.get_live_data(timeframe='1h', limit=5000)

        if df.empty:
            logger.log("⚠️ No se pudieron obtener datos de 1h, intentando con 15m...", "WARNING")
            df = self.trader.data_manager.get_live_data(timeframe='15m', limit=5000)

        if not df.empty:
            logger.log("🤖 Entrenando ensemble ultra mejorado con 10+ modelos ML...")
            logger.log("🔧 Aplicando mejoras v4.1: modelos optimizados, features avanzados, SIN trailing")
            self.is_trained = self.trader.train_system(df)

            if self.is_trained:
                logger.log("✅ Bot v4.1 ULTRA MEJORADO inicializado correctamente")
                logger.log(f"📊 Sistema listo con {len(self.trader.signal_generator.models)} modelos validados")
                logger.log("✨ NUEVAS CARACTERÍSTICAS v4.1:")
                logger.log("  • 🔄 Validación Walk-Forward temporal mejorada")
                logger.log("  • 🏗️ Stacking Ensemble avanzado con meta-learner")
                logger.log("  • 🎯 Feature engineering avanzado (25+ features)")
                logger.log("  • 📊 Criterios de validación más flexibles")
                logger.log("  • 🧹 Filtrado automático de outliers mejorado")
                logger.log("  • 📈 10+ modelos ML optimizados")
                logger.log("  • 💾 Persistencia de modelos entrenados")
                logger.log("  • 🚫 SIN trailing stop/profit - niveles fijos")
                self.show_next_predictions(df)
            else:
                logger.log("❌ Error en inicialización", "ERROR")
        else:
            logger.log("❌ No se pudieron obtener datos", "ERROR")

    def show_next_predictions(self, df):
        """Muestra próximas predicciones con niveles de TP/SL"""
        logger.log("\n" + "="*60)
        logger.log("🔮 PRÓXIMAS PREDICCIONES")
        logger.log("="*60)

        # Crear features
        df_features = self.trader.feature_engineer.create_features(df.copy())

        # Generar señales
        signals = self.trader.signal_generator.generate_signals(df_features)

        if not signals.empty:
            # Mostrar solo la señal actual más reciente
            logger.log("\n📊 SEÑALES DE TRADING:")
            signal = signals.iloc[-1]  # Solo la más reciente
            direction = "🟢 LONG" if signal['signal'] == 1 else "🔴 SHORT"

            # Calcular TP/SL usando los multiplicadores globales
            atr = signal['atr'] if signal['atr'] > 0 else signal['price'] * 0.005
            if signal['signal'] == 1:  # LONG
                stop_loss = signal['price'] - (atr * ATR_MULTIPLIER_SL)
                take_profit = signal['price'] + (atr * ATR_MULTIPLIER_TP)
            else:  # SHORT
                stop_loss = signal['price'] + (atr * ATR_MULTIPLIER_SL)
                take_profit = signal['price'] - (atr * ATR_MULTIPLIER_TP)

            logger.log(f"\n{direction} @ ${signal['price']:.2f}")
            logger.log(f"├─ Stop Loss: ${stop_loss:.2f} ({abs(stop_loss-signal['price'])/signal['price']*100:.2f}%)")
            logger.log(f"├─ Take Profit: ${take_profit:.2f} ({abs(take_profit-signal['price'])/signal['price']*100:.2f}%)")
            logger.log(f"├─ Confianza: {signal['confidence']:.2%}")
            logger.log(f"└─ Hora: {signal['timestamp']}")

            # Mostrar información adicional sobre la señal actual
            logger.log(f"\n📊 DETALLES DE LA SEÑAL ACTUAL:")
            logger.log(f"├─ Consenso entre modelos: {signal['consensus_score']:.1%}")
            logger.log(f"├─ Confianza del ensemble: {signal['ensemble_confidence']:.1%}")
            logger.log(f"├─ ATR utilizado: ${atr:.2f}")
            logger.log(f"└─ Risk/Reward ratio: 1:{abs(take_profit-signal['price'])/abs(stop_loss-signal['price']):.1f}")
        else:
            logger.log("❌ No hay señales de alta confianza en este momento")

        # Mostrar estado del mercado detallado
        current_price = df['close'].iloc[-1]
        rsi = talib.RSI(df['close'])[-1]
        sma_20 = df['close'].rolling(20).mean().iloc[-1]
        sma_50 = df['close'].rolling(50).mean().iloc[-1]
        volume_avg = df['volume'].rolling(20).mean().iloc[-1]
        volatility = df['close'].pct_change().rolling(20).std().iloc[-1] * 100

        # Nuevos indicadores
        adx = talib.ADX(df['high'], df['low'], df['close'])[-1]
        cci = talib.CCI(df['high'], df['low'], df['close'])[-1]
        mfi = talib.MFI(df['high'], df['low'], df['close'], df['volume'])[-1]

        trend = "ALCISTA 📈" if df['close'].iloc[-1] > df['close'].iloc[-20] else "BAJISTA 📉"
        momentum = "FUERTE" if abs(df['close'].iloc[-1] - df['close'].iloc[-5]) / df['close'].iloc[-5] > 0.01 else "DÉBIL"

        asset_symbol = SYMBOL.split('/')[0]

        logger.log(f"\n📊 ESTADO DEL MERCADO {asset_symbol}:")
        logger.log(f"├─ Precio actual: ${current_price:.2f}")
        logger.log(f"├─ RSI: {rsi:.2f} {'(Sobrecompra)' if rsi > 70 else '(Sobreventa)' if rsi < 30 else '(Neutral)'}")
        logger.log(f"├─ ADX: {adx:.2f} {'(Tendencia fuerte)' if adx > 25 else '(Tendencia débil)'}")
        logger.log(f"├─ CCI: {cci:.2f}")
        logger.log(f"├─ MFI: {mfi:.2f}")
        logger.log(f"├─ SMA 20: ${sma_20:.2f} {'↑' if current_price > sma_20 else '↓'}")
        logger.log(f"├─ SMA 50: ${sma_50:.2f} {'↑' if current_price > sma_50 else '↓'}")
        logger.log(f"├─ Tendencia: {trend} ({momentum})")
        logger.log(f"├─ Volatilidad: {volatility:.2f}% {'🔥 ALTA' if volatility > 2 else '✅ NORMAL'}")
        logger.log(f"└─ Volumen: {'📊 Alto' if df['volume'].iloc[-1] > volume_avg * 1.5 else '📉 Normal'}")

    def run_live_simulation(self):
        """Ejecuta bot en modo simulación continua"""
        if not self.is_trained:
            logger.log("❌ Bot no está entrenado", "ERROR")
            logger.log("💡 Por favor, seleccione opción 1 para inicializar el sistema primero")
            return

        asset_symbol = SYMBOL.split('/')[0]

        logger.log(f"\n🤖 INICIANDO TRADING v4.1 ULTRA MEJORADO - {asset_symbol}")
        logger.log(f"💰 Capital inicial: ${self.trader.risk_manager.initial_capital}")
        logger.log(f"📍 Posiciones máximas: {MAX_POSITIONS}")
        logger.log(f"⏰ Timeframe: 1 hora (mayor estabilidad)")
        logger.log("\n📌 CARACTERÍSTICAS v4.1 ULTRA MEJORADAS:")
        logger.log("📌 • 10+ modelos ML optimizados con validación temporal")
        logger.log("📌 • Feature engineering avanzado (25+ features)")
        logger.log("📌 • Stacking Ensemble avanzado con meta-learner")
        logger.log("📌 • Criterios de validación más flexibles")
        logger.log("📌 • Umbrales adaptativos dinámicos optimizados")
        logger.log("📌 • Filtrado automático de outliers mejorado")
        logger.log("📌 • SIN trailing stop/profit - niveles fijos")
        logger.log(f"📌 • Confianza mínima: {MIN_CONFIDENCE:.0%} | Consenso mínimo: {ENSEMBLE_MIN_AGREEMENT:.0%}")
        logger.log(f"📌 • Tolerancia TP/SL: {TP_SL_TOLERANCE:.2%} | Trailing: ❌ DESACTIVADO (niveles fijos)")
        logger.log(f"📌 • ATR Multipliers: SL={ATR_MULTIPLIER_SL}x | TP={ATR_MULTIPLIER_TP}x")
        logger.log("📌 • Filtrado MEJORADO: Criterios más permisivos para generar más señales\n")

        cycle = 0
        while True:
            try:
                cycle += 1
                logger.log(f"\n{'='*60}")
                logger.log(f"📍 Ciclo #{cycle} - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

                # Verificar límite diario
                if self.trader.risk_manager.check_daily_loss_limit():
                    logger.log("🛑 Límite diario alcanzado - Trading pausado")
                    time.sleep(3600)
                    continue

                # Obtener datos actuales con timeframe de 1 hora
                df = self.trader.data_manager.get_live_data(timeframe='1h', limit=500)

                if df.empty:
                    logger.log("⚠️ No se pudieron obtener datos de 1h, usando 15m...", "WARNING")
                    df = self.trader.data_manager.get_live_data(timeframe='15m', limit=500)

                if df.empty:
                    logger.log("⚠️ No hay datos disponibles", "WARNING")
                    time.sleep(60)
                    continue

                # Precio actual
                current_price = df['close'].iloc[-1]

                # Mostrar resumen de mercado
                if cycle <= 10 or cycle % 3 == 0:
                    self.show_market_summary(df)

                # Mostrar información del ciclo
                if cycle == 1:
                    logger.log(f"├─ Sistema: ✅ ACTIVO")
                    if len(self.trader.signal_generator.models) > 0:
                        logger.log(f"├─ Modelos ML: {len(self.trader.signal_generator.models)} activos")
                    else:
                        logger.log("├─ Modelos ML: 0 activos (rechazados por overfitting)")
                        logger.log("├─ Modo: 📊 Análisis técnico puro")
                    logger.log(f"├─ Confianza mínima: {MIN_CONFIDENCE:.0%}")
                logger.log(f"├─ Precio {asset_symbol}: ${current_price:.2f}")
                logger.log(f"├─ Posiciones abiertas: {len([p for p in self.trader.active_positions if p['status'] == 'OPEN'])}/{MAX_POSITIONS}")
                logger.log(f"├─ Trades ejecutados: {self.total_trades_executed}")
                logger.log(f"└─ Capital: ${self.trader.risk_manager.current_capital:.2f}")

                # Obtener ATR actual para trailing stop
                current_atr = None
                if 'atr' in df.columns and not df['atr'].empty:
                    current_atr = df['atr'].iloc[-1]
                    if pd.isna(current_atr) or current_atr <= 0:
                        current_atr = df['close'].iloc[-1] * 0.005
                else:
                    current_atr = df['close'].iloc[-1] * 0.005

                # Actualizar posiciones con precio y ATR actual
                self.trader.update_positions(current_price, current_atr)

                # Generar nuevas señales desde ciclo 2
                if cycle >= 2:
                    logger.log(f"\n🔍 [Ciclo {cycle}] Analizando mercado para oportunidades...")
                    df_features = self.trader.feature_engineer.create_features(df.copy())

                    # Mostrar algunos indicadores clave
                    try:
                        current_rsi = talib.RSI(df['close'])[-1]
                        current_macd = talib.MACD(df['close'])[0][-1]
                        current_adx = talib.ADX(df['high'], df['low'], df['close'])[-1]
                        logger.log(f"📊 Indicadores: RSI={current_rsi:.1f}, MACD={current_macd:.1f}, ADX={current_adx:.1f}")
                    except:
                        pass

                    signals = self.trader.signal_generator.generate_signals(df_features)

                    if not signals.empty:
                        logger.log(f"\n🎯 ¡{len(signals)} SEÑALES DETECTADAS!")

                        # Contar tipos de señales
                        long_signals = len(signals[signals['signal'] == 1])
                        short_signals = len(signals[signals['signal'] == -1])

                        if long_signals > 0:
                            logger.log(f"🟢 LONG: {long_signals} señales")
                        if short_signals > 0:
                            logger.log(f"🔴 SHORT: {short_signals} señales")

                        # Mostrar mejor señal
                        best_signal = signals.loc[signals['confidence'].idxmax()]
                        logger.log(f"🏆 Mejor señal: {'LONG' if best_signal['signal'] == 1 else 'SHORT'} con {best_signal['confidence']:.1%} confianza")

                        # Verificar posiciones abiertas
                        open_positions = [p for p in self.trader.active_positions if p['status'] == 'OPEN']
                        available_slots = MAX_POSITIONS - len(open_positions)

                        # Mostrar posiciones abiertas actual
                        logger.log(f"📊 Posiciones abiertas actuales: {len(open_positions)}")

                        if available_slots > 0:
                            logger.log(f"💼 Espacios disponibles para trades: {available_slots}")

                            # Tomar las mejores señales por confianza
                            signals_sorted = signals.sort_values('confidence', ascending=False)
                            signals_to_process = signals_sorted.head(available_slots)

                            for idx, (_, signal) in enumerate(signals_to_process.iterrows()):
                                logger.log(f"\n🔄 Procesando señal {idx+1}/{len(signals_to_process)}:")
                                logger.log(f"   Tipo: {'🟢 LONG' if signal['signal'] == 1 else '🔴 SHORT'}")
                                logger.log(f"   Precio: ${signal['price']:.2f}")
                                logger.log(f"   Confianza: {signal['confidence']:.2%}")

                                # Ejecutar directamente
                                logger.log("   ✅ EJECUTANDO TRADE")
                                try:
                                    self.trader.execute_trade(signal, df, current_price)
                                    self.total_trades_executed += 1
                                    logger.log("   ✅ Trade ejecutado exitosamente")
                                except Exception as e:
                                    logger.log(f"   ❌ Error ejecutando trade: {str(e)}", "ERROR")

                                # Pequeña pausa para ver la acción
                                time.sleep(1)
                        else:
                            logger.log(f"⚠️ Máximo de posiciones alcanzado ({MAX_POSITIONS}) - esperando cierre de posiciones")
                    else:
                        logger.log(f"❌ No hay señales con confianza suficiente (>{MIN_CONFIDENCE:.0%})")
                        # Mostrar info de debug
                        X, _ = self.trader.signal_generator.prepare_data(df_features)
                        if X is not None and len(X) > 0:
                            logger.log(f"🔍 Sistema analizando {len(X)} patrones de mercado...")

                # Mostrar estado de posiciones abiertas
                open_positions = [p for p in self.trader.active_positions if p['status'] == 'OPEN']
                if open_positions and cycle % 3 == 0:
                    logger.log("\n📋 POSICIONES ABIERTAS:")
                    for i, pos in enumerate(open_positions):
                        pnl_actual = ((current_price - pos['entry_price']) / pos['entry_price'] * 100
                                     if pos['type'] == 'LONG'
                                     else (pos['entry_price'] - current_price) / pos['entry_price'] * 100)

                        # Emoji según PnL
                        pnl_emoji = "🟢" if pnl_actual > 0 else "🔴" if pnl_actual < 0 else "⚪"

                        logger.log(f"{i+1}. {pos['type']} @ ${pos['entry_price']:.2f} | "
                                  f"PnL: {pnl_emoji} {pnl_actual:+.2f}% | "
                                  f"Tiempo: {(datetime.datetime.now() - pos['entry_time']).seconds // 60} min")

                # Mostrar estado cada 5 ciclos
                if cycle % 5 == 0:
                    report = self.trader.generate_report()
                    logger.log(f"\n📊 REPORTE DE RENDIMIENTO:")
                    logger.log(f"├─ Trades totales: {report['total_trades']}")
                    logger.log(f"├─ Win Rate: {report['win_rate']:.1f}%")
                    logger.log(f"├─ Profit Factor: {report['profit_factor']:.2f}")
                    logger.log(f"├─ PnL Total: ${report['total_pnl']:.2f}")
                    logger.log(f"├─ ROI: {report.get('roi', 0):.2f}%")
                    logger.log(f"└─ Capital actual: ${report['current_capital']:.2f}")

                    # Solo mostrar predicciones cada 10 ciclos
                    if cycle % 10 == 0:
                        self.show_next_predictions(df)

                # Esperar antes del próximo ciclo (timeframe 1h = más tiempo)
                time.sleep(120)  # 2 minutos para timeframe de 1 hora

            except KeyboardInterrupt:
                logger.log("\n🛑 Trading detenido por el usuario")
                break
            except Exception as e:
                logger.log(f"❌ Error en ciclo: {str(e)}", "ERROR")
                time.sleep(60)

        # Reporte final
        logger.log("\n" + "="*60)
        logger.log("📊 REPORTE FINAL")
        logger.log("="*60)

        final_report = self.trader.generate_report()
        for key, value in final_report.items():
            if isinstance(value, float):
                logger.log(f"{key}: {value:.2f}")
            else:
                logger.log(f"{key}: {value}")

    def run_backtesting(self):
        """Ejecuta backtesting completo con estadísticas detalladas por mes y año"""
        logger.log("="*60)
        logger.log("📊 INICIANDO BACKTESTING COMPLETO")
        logger.log("="*60)

        # Solicitar período de backtesting
        print("\n📅 Configuración del Backtesting:")
        print("1. Último año (recomendado)")
        print("2. Últimos 6 meses")
        print("3. Últimos 3 meses")
        print("4. Personalizado")

        periodo = input("\nSeleccione período (1-4): ")

        if periodo == '1':
            days = 365
            logger.log("📅 Período seleccionado: Último año (365 días)")
        elif periodo == '2':
            days = 180
            logger.log("📅 Período seleccionado: Últimos 6 meses (180 días)")
        elif periodo == '3':
            days = 90
            logger.log("📅 Período seleccionado: Últimos 3 meses (90 días)")
        elif periodo == '4':
            try:
                days = int(input("Ingrese número de días: "))
                logger.log(f"📅 Período personalizado: {days} días")
            except:
                days = 90
                logger.log("❌ Valor inválido, usando 90 días por defecto")
        else:
            days = 365
            logger.log("📅 Usando período por defecto: Último año")

        # Obtener datos históricos
        logger.log(f"📚 Descargando {days} días de datos históricos...")
        limit = days * 24  # Calcular velas necesarias para el período solicitado
        df = self.trader.data_manager.get_live_data(timeframe='1h', limit=limit)

        if df.empty:
            logger.log("❌ No se pudieron obtener datos históricos", "ERROR")
            return

        logger.log(f"✅ Datos obtenidos: {len(df)} velas de 1h")
        logger.log(f"📅 Desde: {df.index[0]} hasta: {df.index[-1]}")

        # Ejecutar backtesting
        backtest_results = self._execute_backtesting(df)

        # Mostrar resultados detallados
        self._display_backtesting_results(backtest_results, df)

    def _execute_backtesting(self, df):
        """Ejecuta el backtesting sobre los datos históricos"""
        logger.log("🔄 Ejecutando backtesting...")

        # Resetear el trader para backtesting
        original_capital = self.trader.risk_manager.initial_capital
        self.trader.risk_manager.current_capital = original_capital
        self.trader.active_positions = []
        self.trader.closed_positions = []

        # IMPORTANTE: Usar el mismo procesamiento de datos que en entrenamiento
        logger.log("📊 Preparando datos completos para backtesting...")

        # Procesar todos los datos de una vez para mantener consistencia
        df_processed = self.trader.feature_engineer.create_features(df.copy())

        # CRÍTICO: Aplicar el mismo procesamiento completo que en prepare_data
        # Crear log_returns si no existe
        if 'log_returns' not in df_processed.columns:
            df_processed['log_returns'] = np.log(df_processed['close'] / df_processed['close'].shift(1))

        # Crear future_return para targets (aunque no lo usemos en backtesting)
        df_processed['future_return'] = df_processed['close'].pct_change(1).shift(-1)

        # Crear targets (necesarios para el procesamiento)
        returns_clean = df_processed['future_return'].dropna()
        if len(returns_clean) > 0:
            lower_percentile = returns_clean.quantile(0.33)
            upper_percentile = returns_clean.quantile(0.67)

            df_processed['target'] = np.where(
                df_processed['future_return'] < lower_percentile, -1,
                np.where(df_processed['future_return'] > upper_percentile, 1, 0)
            )
            df_processed['target_ml'] = df_processed['target'] + 1

        # IMPORTANTE: Crear las features que faltan usando los mismos métodos
        df_processed['market_regime'] = self.trader.signal_generator._calculate_market_regime(df_processed)
        df_processed['volatility_regime'] = self.trader.signal_generator._calculate_volatility_regime(df_processed)
        df_processed = self.trader.signal_generator._add_microstructure_features(df_processed)

        # Para backtesting, necesitamos mantener el DataFrame completo, no solo X, y
        if df_processed is None or len(df_processed) < 200:
            logger.log("❌ Datos insuficientes para backtesting", "ERROR")
            return []

        # Configurar para backtesting
        window_size = 100  # Ventana deslizante para entrenamiento
        results = []

        # Simular trading día por día usando datos ya procesados
        for i in range(window_size, len(df_processed)):
            try:
                current_price = df_processed.iloc[i]['close']
                current_time = df_processed.index[i]

                # Usar datos hasta el momento actual
                current_data = df_processed.iloc[:i+1].copy()

                # Generar señales usando datos ya procesados
                signals = self._generate_backtest_signals(current_data)

                if not signals.empty:
                    signal = signals.iloc[-1]

                    # Ejecutar trade simulado
                    trade_result = self._simulate_trade(signal, current_price, current_time, df_processed.iloc[i:])
                    if trade_result:
                        results.append(trade_result)

                # Actualizar posiciones existentes
                self._update_backtest_positions(current_price, current_time)

                # Log progreso cada 100 iteraciones
                if i % 100 == 0:
                    progress = (i / len(df_processed)) * 100
                    logger.log(f"📊 Progreso: {progress:.1f}% - Trades: {len(results)}")

            except Exception as e:
                logger.log(f"⚠️ Error en iteración {i}: {str(e)}", "WARNING")
                continue

        logger.log(f"✅ Backtesting completado - {len(results)} trades ejecutados")
        return results

    def _generate_backtest_signals(self, df):
        """Genera señales para backtesting usando modelos ya entrenados"""
        try:
            # Usar solo las features que fueron seleccionadas en el entrenamiento
            if not hasattr(self.trader.signal_generator, 'feature_cols') or not self.trader.signal_generator.feature_cols:
                logger.log("⚠️ No hay features seleccionadas", "WARNING")
                return pd.DataFrame()

            # IMPORTANTE: Usar exactamente las mismas features del entrenamiento
            feature_cols = self.trader.signal_generator.feature_cols

            # Verificar que todas las features necesarias están disponibles
            missing_cols = [col for col in feature_cols if col not in df.columns]
            if missing_cols:
                logger.log(f"⚠️ Features faltantes: {missing_cols}", "WARNING")
                return pd.DataFrame()

            # Extraer exactamente las mismas features en el mismo orden
            X = df[feature_cols].iloc[-1:].values  # Solo la última fila

            # Verificar dimensiones
            expected_features = len(feature_cols)
            actual_features = X.shape[1]
            if actual_features != expected_features:
                logger.log(f"⚠️ Mismatch de features: esperado {expected_features}, obtenido {actual_features}", "WARNING")
                return pd.DataFrame()

            # Verificar que tenemos modelos entrenados
            if not self.trader.signal_generator.models or not self.trader.signal_generator.ensemble:
                logger.log("⚠️ No hay modelos entrenados para backtesting", "WARNING")
                return pd.DataFrame()

            # Aplicar el mismo escalado que en entrenamiento
            if hasattr(self.trader.signal_generator, 'scaler') and self.trader.signal_generator.scaler:
                X = self.trader.signal_generator.scaler.transform(X)

            # Generar predicción usando el ensemble
            try:
                prediction = self.trader.signal_generator.ensemble.predict(X)[0]
                probabilities = self.trader.signal_generator.ensemble.predict_proba(X)[0]
                confidence = max(probabilities)

                # Crear señal
                signals = pd.DataFrame({
                    'timestamp': [df.index[-1]],
                    'signal': [prediction],
                    'confidence': [confidence],
                    'ensemble_confidence': [confidence],
                    'consensus_score': [confidence],
                    'price': [df.iloc[-1]['close']],
                    'atr': [df.iloc[-1].get('atr', df.iloc[-1]['close'] * 0.01)],
                    'quality_score': [1.0],
                    'model_count': [len(self.trader.signal_generator.models)]
                })

                return signals

            except Exception as e:
                logger.log(f"⚠️ Error en predicción: {str(e)}", "WARNING")
                return pd.DataFrame()

        except Exception as e:
            logger.log(f"❌ Error generando señales de backtesting: {str(e)}", "ERROR")
            return pd.DataFrame()

    def _simulate_trade(self, signal, current_price, current_time, future_data):
        """Simula la ejecución de un trade"""
        try:
            # Verificar si ya tenemos posiciones abiertas
            open_positions = [p for p in self.trader.active_positions if p['status'] == 'OPEN']
            if len(open_positions) >= MAX_POSITIONS:
                return None

            # Calcular niveles de TP/SL
            atr = signal.get('atr', current_price * 0.005)

            if signal['signal'] == 1:  # LONG
                stop_loss = current_price - (atr * ATR_MULTIPLIER_SL)
                take_profit = current_price + (atr * ATR_MULTIPLIER_TP)
                trade_type = 'LONG'
            else:  # SHORT
                stop_loss = current_price + (atr * ATR_MULTIPLIER_SL)
                take_profit = current_price - (atr * ATR_MULTIPLIER_TP)
                trade_type = 'SHORT'

            # Calcular tamaño de posición dinámico
            position_size = self.trader.risk_manager.calculate_position_size(signal['confidence'])

            # Crear posición
            position = {
                'id': len(self.trader.active_positions) + len(self.trader.closed_positions) + 1,
                'type': trade_type,
                'entry_price': current_price,
                'entry_time': current_time,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'size': position_size,
                'position_size': position_size,  # Para compatibilidad
                'status': 'OPEN',
                'confidence': signal['confidence']
            }

            self.trader.active_positions.append(position)

            # Simular el resultado del trade usando datos futuros
            return self._simulate_trade_outcome(position, future_data)

        except Exception as e:
            logger.log(f"❌ Error simulando trade: {str(e)}", "ERROR")
            return None

    def _simulate_trade_outcome(self, position, future_data):
        """Simula el resultado de un trade usando datos futuros"""
        try:
            entry_price = position['entry_price']
            stop_loss = position['stop_loss']
            take_profit = position['take_profit']
            trade_type = position['type']

            # Buscar en datos futuros hasta que se active TP o SL
            for i, (timestamp, row) in enumerate(future_data.iterrows()):
                high = row['high']
                low = row['low']
                close = row['close']

                # Verificar si se activó SL o TP
                if trade_type == 'LONG':
                    if low <= stop_loss:
                        # Stop Loss activado
                        exit_price = stop_loss
                        exit_reason = 'SL'
                        exit_time = timestamp
                        break
                    elif high >= take_profit:
                        # Take Profit activado
                        exit_price = take_profit
                        exit_reason = 'TP'
                        exit_time = timestamp
                        break
                else:  # SHORT
                    if high >= stop_loss:
                        # Stop Loss activado
                        exit_price = stop_loss
                        exit_reason = 'SL'
                        exit_time = timestamp
                        break
                    elif low <= take_profit:
                        # Take Profit activado
                        exit_price = take_profit
                        exit_reason = 'TP'
                        exit_time = timestamp
                        break

                # Límite de tiempo (máximo 24 horas)
                if i >= 24:
                    exit_price = close
                    exit_reason = 'TIME'
                    exit_time = timestamp
                    break
            else:
                # Si no se activó nada, cerrar al final
                exit_price = future_data.iloc[-1]['close']
                exit_reason = 'END'
                exit_time = future_data.index[-1]

            # Calcular PnL
            if trade_type == 'LONG':
                pnl_pct = (exit_price - entry_price) / entry_price
            else:
                pnl_pct = (entry_price - exit_price) / entry_price

            pnl_usd = pnl_pct * position['position_size']

            # Actualizar posición
            position['exit_price'] = exit_price
            position['exit_time'] = exit_time
            position['exit_reason'] = exit_reason
            position['pnl_pct'] = pnl_pct
            position['pnl_usd'] = pnl_usd
            position['status'] = 'CLOSED'

            # Mover a posiciones cerradas
            self.trader.active_positions.remove(position)
            self.trader.closed_positions.append(position)

            # Actualizar capital
            self.trader.risk_manager.current_capital += pnl_usd

            return position

        except Exception as e:
            logger.log(f"❌ Error simulando resultado: {str(e)}", "ERROR")
            return None

    def _update_backtest_positions(self, current_price, current_time):
        """Actualiza posiciones abiertas durante el backtesting"""
        # En backtesting, las posiciones se cierran automáticamente en _simulate_trade_outcome
        pass

    def _display_backtesting_results(self, results, df):
        """Muestra resultados detallados del backtesting por mes y año"""
        if not results:
            logger.log("❌ No se ejecutaron trades en el backtesting", "ERROR")
            return

        logger.log("="*60)
        logger.log("📊 RESULTADOS DEL BACKTESTING")
        logger.log("="*60)

        # Convertir a DataFrame para análisis
        import pandas as pd
        trades_df = pd.DataFrame(results)
        trades_df['entry_time'] = pd.to_datetime(trades_df['entry_time'])
        trades_df['exit_time'] = pd.to_datetime(trades_df['exit_time'])
        trades_df['month'] = trades_df['entry_time'].dt.to_period('M')
        trades_df['year'] = trades_df['entry_time'].dt.year

        # Estadísticas generales
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl_usd'] > 0])
        losing_trades = len(trades_df[trades_df['pnl_usd'] < 0])
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0

        total_pnl = trades_df['pnl_usd'].sum()
        total_pnl_pct = (total_pnl / INITIAL_CAPITAL) * 100

        avg_win = trades_df[trades_df['pnl_usd'] > 0]['pnl_usd'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl_usd'] < 0]['pnl_usd'].mean() if losing_trades > 0 else 0
        profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if losing_trades > 0 and avg_loss != 0 else float('inf')

        # Estadísticas por tipo de salida
        tp_trades = len(trades_df[trades_df['exit_reason'] == 'TP'])
        sl_trades = len(trades_df[trades_df['exit_reason'] == 'SL'])
        time_trades = len(trades_df[trades_df['exit_reason'] == 'TIME'])

        logger.log("📈 RESUMEN GENERAL:")
        logger.log(f"├─ Total de trades: {total_trades}")
        logger.log(f"├─ Trades ganadores: {winning_trades} ({win_rate:.1f}%)")
        logger.log(f"├─ Trades perdedores: {losing_trades} ({100-win_rate:.1f}%)")
        logger.log(f"├─ Win Rate: {win_rate:.1f}%")
        logger.log(f"├─ Profit Factor: {profit_factor:.2f}")
        logger.log(f"├─ PnL Total: ${total_pnl:.2f} ({total_pnl_pct:+.2f}%)")
        logger.log(f"├─ Ganancia promedio: ${avg_win:.2f}")
        logger.log(f"├─ Pérdida promedio: ${avg_loss:.2f}")
        logger.log(f"├─ Take Profit: {tp_trades} ({tp_trades/total_trades*100:.1f}%)")
        logger.log(f"├─ Stop Loss: {sl_trades} ({sl_trades/total_trades*100:.1f}%)")
        logger.log(f"└─ Por tiempo: {time_trades} ({time_trades/total_trades*100:.1f}%)")

        # Análisis por año
        logger.log("\n📅 ANÁLISIS POR AÑO:")
        yearly_stats = trades_df.groupby('year').agg({
            'pnl_usd': ['count', 'sum'],
            'pnl_pct': 'mean'
        }).round(2)

        # Calcular wins por separado para evitar problemas con lambda
        yearly_wins = trades_df[trades_df['pnl_usd'] > 0].groupby('year').size()

        for year in yearly_stats.index:
            year_trades = yearly_stats.loc[year, ('pnl_usd', 'count')]
            year_pnl = yearly_stats.loc[year, ('pnl_usd', 'sum')]
            year_wins = yearly_wins.get(year, 0)
            year_wr = (year_wins / year_trades * 100) if year_trades > 0 else 0
            year_roi = (year_pnl / INITIAL_CAPITAL) * 100

            logger.log(f"🗓️ {year}:")
            logger.log(f"   ├─ Trades: {year_trades}")
            logger.log(f"   ├─ Win Rate: {year_wr:.1f}%")
            logger.log(f"   ├─ PnL: ${year_pnl:.2f}")
            logger.log(f"   └─ ROI: {year_roi:+.2f}%")

        # Análisis por mes (últimos 12 meses)
        logger.log("\n📅 ANÁLISIS POR MES (Últimos disponibles):")
        monthly_stats = trades_df.groupby('month').agg({
            'pnl_usd': ['count', 'sum'],
            'pnl_pct': 'mean'
        }).round(2)

        # Calcular wins por separado
        monthly_wins = trades_df[trades_df['pnl_usd'] > 0].groupby('month').size()

        # Mostrar últimos 12 meses o todos si hay menos
        recent_months = monthly_stats.tail(12)

        for month in recent_months.index:
            month_trades = recent_months.loc[month, ('pnl_usd', 'count')]
            month_pnl = recent_months.loc[month, ('pnl_usd', 'sum')]
            month_wins = monthly_wins.get(month, 0)
            month_wr = (month_wins / month_trades * 100) if month_trades > 0 else 0
            month_roi = (month_pnl / INITIAL_CAPITAL) * 100

            # Emoji según performance
            perf_emoji = "🟢" if month_pnl > 0 else "🔴" if month_pnl < 0 else "⚪"

            logger.log(f"📅 {month} {perf_emoji}:")
            logger.log(f"   ├─ Trades: {month_trades}")
            logger.log(f"   ├─ Win Rate: {month_wr:.1f}%")
            logger.log(f"   ├─ PnL: ${month_pnl:.2f}")
            logger.log(f"   └─ ROI: {month_roi:+.2f}%")

        # Mejores y peores trades
        logger.log("\n🏆 MEJORES Y PEORES TRADES:")
        best_trade = trades_df.loc[trades_df['pnl_usd'].idxmax()]
        worst_trade = trades_df.loc[trades_df['pnl_usd'].idxmin()]

        logger.log(f"🥇 Mejor trade:")
        logger.log(f"   ├─ Tipo: {best_trade['type']}")
        logger.log(f"   ├─ Fecha: {best_trade['entry_time'].strftime('%Y-%m-%d %H:%M')}")
        logger.log(f"   ├─ PnL: ${best_trade['pnl_usd']:.2f} ({best_trade['pnl_pct']*100:+.2f}%)")
        logger.log(f"   └─ Salida: {best_trade['exit_reason']}")

        logger.log(f"🥉 Peor trade:")
        logger.log(f"   ├─ Tipo: {worst_trade['type']}")
        logger.log(f"   ├─ Fecha: {worst_trade['entry_time'].strftime('%Y-%m-%d %H:%M')}")
        logger.log(f"   ├─ PnL: ${worst_trade['pnl_usd']:.2f} ({worst_trade['pnl_pct']*100:+.2f}%)")
        logger.log(f"   └─ Salida: {worst_trade['exit_reason']}")

        # Análisis de drawdown
        trades_df['cumulative_pnl'] = trades_df['pnl_usd'].cumsum()
        trades_df['running_max'] = trades_df['cumulative_pnl'].expanding().max()
        trades_df['drawdown'] = trades_df['cumulative_pnl'] - trades_df['running_max']

        max_drawdown = trades_df['drawdown'].min()
        max_drawdown_pct = (max_drawdown / INITIAL_CAPITAL) * 100

        logger.log(f"\n📉 ANÁLISIS DE RIESGO:")
        logger.log(f"├─ Máximo Drawdown: ${max_drawdown:.2f} ({max_drawdown_pct:.2f}%)")
        logger.log(f"├─ Capital final: ${INITIAL_CAPITAL + total_pnl:.2f}")
        logger.log(f"└─ ROI Total: {total_pnl_pct:+.2f}%")

        # Guardar resultados en archivo
        try:
            timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
            filename = f"backtest_results_{timestamp}.csv"
            trades_df.to_csv(filename, index=False)
            logger.log(f"\n💾 Resultados guardados en: {filename}")
        except Exception as e:
            logger.log(f"⚠️ No se pudo guardar archivo: {str(e)}", "WARNING")

        logger.log("="*60)
        logger.log("✅ BACKTESTING COMPLETADO")
        logger.log("="*60)

def main():
    """Función principal"""
    asset_symbol = SYMBOL.split('/')[0]

    print("\n" + "="*70)
    print(f"🚀 {asset_symbol} TRADING BOT ULTRA OPTIMIZADO v4.5")
    print("\n⚡ IMPORTANTE:")
    print("1. Primero DEBES inicializar el sistema (opción 1)")
    print("2. El bot usa ML optimizado con 10+ modelos avanzados")
    print("3. Sistema SIN trailing stop/profit - niveles fijos para máxima precisión")
    print("4. Los resultados son 100% simulados - NO es dinero real")
    print("="*70)
    print("�️ NUEVAS MEJORAS ANTI-OVERFITTING v4.2:")
    print("  • 🤖 10+ modelos ML ultra simples (RF, XGB, LightGBM, CatBoost, MLP, SVM, etc.)")
    print("  • 🔄 Validación Walk-Forward temporal robusta")
    print("  • 🏗️ Stacking Ensemble con regularización extrema")
    print("  • 🎯 Feature selection ultra restrictivo (solo 10 features)")
    print("  • 📊 Regularización extrema en todos los modelos")
    print("  • 🧹 Filtrado automático de outliers")
    print("  • 📈 Criterios de validación anti-overfitting")
    print("  • 💾 Persistencia de modelos entrenados")
    print("  • 🔍 Validación cruzada temporal de 7 folds")
    print("  • ⚖️ Balance automático de clases")
    print("  • 🎲 Regularización L1, L2 y ElasticNet extrema")
    print("  • 🚫 SIN trailing stop/profit - niveles fijos desde entrada")
    print("  • 📊 ATR multipliers optimizados: SL=2.5x, TP=4.0x")
    print("  • 🛡️ Modelos ultra simples para evitar overfitting")
    print("="*70)

    bot = LiveTradingBot()

    # Menú principal
    while True:
        print("\nSeleccione opción:")
        print("1. Inicializar/Reinicializar sistema")
        print("2. Ejecutar simulación en vivo ⭐ (RECOMENDADO)")
        print("3. Ver predicciones actuales")
        print("4. Ejecutar backtesting completo 📊")
        print("5. Optimización agresiva de parámetros 🚀")
        print("6. Optimización ultra-fina para ROI 🎯")
        print("7. Optimización Profit Factor > 1.0 ⚖️ (NUEVO)")
        print("8. Salir")

        opcion = input("\nOpción (1-8): ")

        if opcion == '1':
            print("\n🔄 Inicializando sistema...")
            print("⏳ Esto puede tomar 30-60 segundos...")
            bot.initialize()
            print("\n✅ Sistema listo para trading!")
            print("💡 Ahora seleccione opción 2 para comenzar la simulación")
        elif opcion == '2':
            if bot.is_trained:
                bot.run_live_simulation()
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
                print("💡 El sistema necesita ser entrenado antes de ejecutar la simulación")
        elif opcion == '3':
            if bot.is_trained:
                df = bot.trader.data_manager.get_live_data(timeframe='1h', limit=500)
                if df.empty:
                    df = bot.trader.data_manager.get_live_data(timeframe='15m', limit=500)
                bot.show_next_predictions(df)
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
        elif opcion == '4':
            if bot.is_trained:
                bot.run_backtesting()
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
                print("💡 El sistema necesita ser entrenado antes de ejecutar el backtesting")
        elif opcion == '5':
            if bot.is_trained:
                print("\n🚀 Iniciando optimización agresiva de parámetros...")
                print("⏳ Esto puede tomar 5-10 minutos...")

                # Obtener datos para optimización
                df = bot.trader.data_manager.get_live_data(timeframe='1h', limit=1000)
                if not df.empty:
                    # Ejecutar optimización
                    best_params = bot.optimizer.optimize_parameters(df)

                    if best_params:
                        # Mostrar resultados
                        bot.optimizer.display_optimization_summary()

                        # Preguntar si aplicar los mejores parámetros
                        aplicar = input("\n¿Aplicar los mejores parámetros encontrados? (s/n): ")
                        if aplicar.lower() in ['s', 'si', 'y', 'yes']:
                            bot.optimizer.apply_best_parameters()
                            print("✅ Parámetros optimizados aplicados")
                            print("💡 Ahora puede ejecutar backtesting para ver las mejoras")
                        else:
                            print("ℹ️ Parámetros no aplicados - manteniendo configuración actual")
                    else:
                        print("❌ No se pudieron optimizar los parámetros")
                else:
                    print("❌ No se pudieron obtener datos para optimización")
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
                print("💡 El sistema necesita ser entrenado antes de optimizar parámetros")

        elif opcion == '6':
            if bot.is_trained:
                print("\n🎯 Iniciando optimización ultra-fina para ROI...")
                print("⏳ Esto puede tomar 3-5 minutos...")

                # Obtener datos para optimización
                df = bot.trader.data_manager.get_live_data(timeframe='1h', limit=1000)
                if not df.empty:
                    # Usar parámetros de la optimización anterior como base
                    base_params = bot.optimizer.best_params if bot.optimizer.best_params else None

                    # Ejecutar optimización ultra-fina
                    best_params, metrics = bot.optimizer.optimize_for_roi(df, base_params)

                    if best_params and metrics:
                        print(f"\n🎉 Optimización ultra-fina completada!")
                        print(f"📊 ROI: {metrics['roi']*100:.2f}%")
                        print(f"📈 Win Rate: {metrics['win_rate']:.1%}")
                        print(f"📊 Profit Factor: {metrics['profit_factor']:.2f}")

                        # Preguntar si aplicar los mejores parámetros
                        aplicar = input("\n¿Aplicar los parámetros ultra-optimizados? (s/n): ")
                        if aplicar.lower() in ['s', 'si', 'y', 'yes']:
                            bot.optimizer.apply_best_parameters()
                            print("✅ Parámetros ultra-optimizados aplicados")
                            print("💡 Ejecute backtesting para verificar las mejoras")
                        else:
                            print("ℹ️ Parámetros no aplicados - manteniendo configuración actual")
                    else:
                        print("❌ No se pudieron optimizar los parámetros ultra-finos")
                else:
                    print("❌ No se pudieron obtener datos para optimización")
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
                print("💡 El sistema necesita ser entrenado antes de optimizar parámetros")

        elif opcion == '7':
            if bot.is_trained:
                print("\n⚖️ Iniciando optimización Profit Factor > 1.0...")
                print("⏳ Esto puede tomar 4-6 minutos...")
                print("🎯 Objetivo: Conseguir Profit Factor > 1.0 para rentabilidad sostenible")

                # Obtener datos para optimización
                df = bot.trader.data_manager.get_live_data(timeframe='1h', limit=1000)
                if not df.empty:
                    # Ejecutar optimización de Profit Factor
                    best_params, metrics = bot.optimizer.optimize_profit_factor(df, target_pf=1.2)

                    if best_params and metrics:
                        pf = metrics['profit_factor']
                        print(f"\n⚖️ Optimización Profit Factor completada!")
                        print(f"📊 Profit Factor: {pf:.2f}")
                        print(f"🏆 ROI: {metrics['roi']*100:.2f}%")
                        print(f"📈 Win Rate: {metrics['win_rate']:.1%}")
                        print(f"💰 Ganancia promedio: ${metrics.get('avg_win', 0):.2f}")
                        print(f"💸 Pérdida promedio: ${metrics.get('avg_loss', 0):.2f}")

                        if pf >= 1.0:
                            print(f"🎉 ¡PROFIT FACTOR > 1.0 CONSEGUIDO!")
                            print(f"✅ Sistema ahora es rentable sosteniblemente")
                        else:
                            print(f"📈 Progreso hacia PF = 1.0: {(pf/1.0)*100:.1f}%")

                        # Preguntar si aplicar los mejores parámetros
                        aplicar = input("\n¿Aplicar los parámetros optimizados para Profit Factor? (s/n): ")
                        if aplicar.lower() in ['s', 'si', 'y', 'yes']:
                            bot.optimizer.apply_best_parameters()
                            print("✅ Parámetros optimizados para PF aplicados")
                            print("💡 Ejecute backtesting para verificar la rentabilidad")
                        else:
                            print("ℹ️ Parámetros no aplicados - manteniendo configuración actual")
                    else:
                        print("❌ No se pudieron optimizar los parámetros para Profit Factor")
                else:
                    print("❌ No se pudieron obtener datos para optimización")
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
                print("💡 El sistema necesita ser entrenado antes de optimizar parámetros")

        elif opcion == '8':
            print(f"👋 Gracias por usar {asset_symbol} Trading Bot ULTRA OPTIMIZADO v4.5")
            print("⚖️ Sistema con optimización Profit Factor > 1.0")
            break
        else:
            print("❌ Opción inválida")

if __name__ == "__main__":
    main()
